# CoachTeacher 微信小程序开发计划

## 项目概述
创建一个微信小程序"CoachTeacher"，用于教练和学员之间的课程管理和训练分析。

## 技术栈
- **前端**: 原生微信小程序
- **后端**: Python + FastAPI
- **数据库**: Supabase (PostgreSQL + 实时功能)
- **认证**: 微信登录 + 角色权限管理
- **通信**: 微信小程序订阅消息
- **部署**: 云服务器 (推荐腾讯云/阿里云)

## 核心功能模块

### 1. 用户认证与权限管理
- 微信登录集成
- 用户角色管理 (教练/学员)
- 权限控制中间件

### 2. 教练端功能
- 课程排期管理 (创建/查看/修改/删除)
- 学员管理
- 课程提醒设置
- 训练强度数据录入

### 3. 学员端功能
- 查看教练排期
- 课程时间沟通确认
- 接收课程提醒
- 查看个人分析报告

### 4. 数据分析模块
- 体测报告数据处理
- 训练强度分析
- 可视化图表展示

## 开发阶段计划

### 阶段1: 项目初始化与基础架构 (状态: 已完成)
- [x] 创建项目目录结构
- [x] 初始化微信小程序项目
- [x] 设置FastAPI后端项目
- [x] 配置Supabase数据库
- [x] 设计数据库表结构
- [x] 配置开发环境

### 阶段2: 用户认证系统 (状态: 已完成)
- [x] 实现微信登录功能
- [x] 创建用户角色管理
- [x] 实现JWT认证中间件
- [x] 权限控制系统

### 阶段3: 核心业务功能 (状态: 已完成)
- [x] 课程排期CRUD接口
- [x] 学员管理功能
- [x] 课程沟通确认功能
- [ ] 实时通信集成

### 阶段4: 数据分析功能 (状态: 已完成)
- [x] 体测报告数据模型
- [x] 训练强度分析算法
- [x] 数据可视化组件
- [x] 报告生成功能

### 阶段5: UI设计与前端实现 (状态: 已完成)
- [x] UI原型设计
- [x] 小程序页面开发
- [x] 组件库建设
- [x] 响应式适配

### 阶段6: 测试与优化 (状态: 已完成)
- [x] 单元测试编写
- [x] 集成测试
- [x] 性能优化
- [x] 用户体验优化

### 阶段7: 部署与发布 (状态: 待办)
- [x] 生产环境配置
- [x] 小程序审核准备
- [ ] 部署上线
- [ ] 监控与维护

## 数据库设计概要

### 核心表结构
1. **users** - 用户基础信息
2. **coaches** - 教练详细信息
3. **students** - 学员详细信息
4. **schedules** - 课程排期
5. **fitness_reports** - 体测报告
6. **training_records** - 训练记录
7. **notifications** - 通知消息

## 预估时间
- 总开发时间: 3-4周
- 每个阶段: 3-5天
- 测试与优化: 1周

## 风险评估
- 微信小程序API限制
- Supabase实时功能集成复杂度
- 体测数据格式兼容性
- 订阅消息审核要求

## 下一步行动
开始阶段1: 项目初始化与基础架构
