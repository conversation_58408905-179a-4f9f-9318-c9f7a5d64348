# CoachTeacher 微信小程序

一个专为教练和学员设计的课程管理微信小程序，提供排期管理、实时沟通和训练分析功能。

## 功能特性

### 教练端
- 📅 课程排期管理（创建、修改、删除）
- 👥 学员管理
- 🔔 课程提醒设置
- 📊 训练强度数据录入

### 学员端
- 📋 查看教练排期
- 💬 课程时间沟通确认
- 🔔 接收课程提醒
- 📈 个人分析报告查看

### 数据分析
- 🏥 体测报告数据处理
- 💪 训练强度分析
- 📊 可视化图表展示

## 技术栈

- **前端**: 原生微信小程序
- **后端**: Python + FastAPI
- **数据库**: Supabase (PostgreSQL)
- **认证**: 微信登录 + JWT
- **实时通信**: 微信订阅消息

## 项目结构

```
CoachTeacher/
├── miniprogram/          # 微信小程序前端
│   ├── pages/           # 页面文件
│   ├── components/      # 组件
│   ├── utils/          # 工具函数
│   └── app.js          # 小程序入口
├── backend/             # FastAPI后端
│   ├── app/            # 应用核心
│   ├── models/         # 数据模型
│   ├── api/            # API路由
│   └── main.py         # 后端入口
├── docs/               # 文档
├── tests/              # 测试文件
└── deployment/         # 部署配置
```

## 🚀 快速开始

### 📋 环境要求
- Python 3.8+
- 微信开发者工具
- Supabase账号（免费）

### ⚡ 一键演示
```bash
# 克隆项目
git clone <your-repo-url>
cd CoachTeacher

# 运行演示脚本
./demo.sh
```

### 🔧 手动安装

#### 1. 后端设置
```bash
cd backend
pip3 install fastapi uvicorn python-dotenv pydantic-settings supabase
```

#### 2. 数据库配置
1. 访问 [Supabase](https://supabase.com) 创建项目
2. 执行 `backend/database_schema.sql`
3. 配置环境变量

#### 3. 启动服务
```bash
cd backend
python3 -m uvicorn main:app --reload
```

#### 4. 小程序开发
使用微信开发者工具打开 `miniprogram` 目录

### 📖 详细文档
- [快速开始指南](./QUICK_START.md)
- [开发文档](./docs/Development_Guide.md)
- [UI设计原型](./docs/UI_Design_Prototype.md)

## 开发文档

详细的开发文档请查看 [docs](./docs/) 目录。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
