#!/bin/bash

# CoachTeacher GitHub推送脚本
# 用于将本地代码推送到GitHub仓库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色文本
print_color() {
    printf "${2}${1}${NC}\n"
}

echo "🚀 CoachTeacher GitHub推送脚本"
echo "================================"
echo ""

# 检查是否在正确的目录
if [ ! -f "README.md" ] || [ ! -d ".git" ]; then
    print_color "❌ 错误: 请在CoachTeacher项目根目录运行此脚本" $RED
    exit 1
fi

# 检查Git状态
print_color "📋 检查Git状态..." $BLUE
git status --porcelain

if [ -n "$(git status --porcelain)" ]; then
    print_color "⚠️  发现未提交的更改，正在提交..." $YELLOW
    git add .
    git commit -m "chore: Update before pushing to GitHub"
fi

# 获取GitHub仓库URL
echo ""
print_color "🔗 请提供您的GitHub仓库信息:" $BLUE
echo "格式: https://github.com/YOUR_USERNAME/CoachTeacher.git"
echo "或者: **************:YOUR_USERNAME/CoachTeacher.git"
echo ""
read -p "请输入GitHub仓库URL: " REPO_URL

if [ -z "$REPO_URL" ]; then
    print_color "❌ 错误: 仓库URL不能为空" $RED
    exit 1
fi

# 检查是否已有远程仓库
if git remote get-url origin >/dev/null 2>&1; then
    print_color "⚠️  检测到已存在的远程仓库:" $YELLOW
    git remote get-url origin
    read -p "是否要更新远程仓库URL? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git remote set-url origin "$REPO_URL"
        print_color "✅ 远程仓库URL已更新" $GREEN
    fi
else
    # 添加远程仓库
    print_color "🔗 添加远程仓库..." $BLUE
    git remote add origin "$REPO_URL"
    print_color "✅ 远程仓库已添加" $GREEN
fi

# 确保在main分支
print_color "🌿 确保在main分支..." $BLUE
git branch -M main

# 推送到GitHub
print_color "📤 推送代码到GitHub..." $BLUE
echo "正在推送到: $REPO_URL"

if git push -u origin main; then
    print_color "✅ 代码推送成功!" $GREEN
    echo ""
    print_color "🎉 您的项目现已在GitHub上!" $GREEN
    echo ""
    
    # 提取仓库URL用于显示
    REPO_NAME=$(echo "$REPO_URL" | sed 's/.*github.com[:/]\([^/]*\/[^/]*\)\.git/\1/')
    if [ -n "$REPO_NAME" ]; then
        print_color "🌐 项目地址: https://github.com/$REPO_NAME" $BLUE
        print_color "📚 API文档: https://github.com/$REPO_NAME#readme" $BLUE
    fi
    
    echo ""
    print_color "📋 下一步建议:" $BLUE
    echo "1. 访问GitHub仓库页面"
    echo "2. 添加项目描述和标签"
    echo "3. 创建第一个Release"
    echo "4. 启用Issues和Discussions"
    echo "5. 配置GitHub Pages (如果需要)"
    
else
    print_color "❌ 推送失败!" $RED
    echo ""
    print_color "🔧 可能的解决方案:" $YELLOW
    echo "1. 检查GitHub仓库是否已创建"
    echo "2. 确认仓库URL是否正确"
    echo "3. 检查GitHub访问权限"
    echo "4. 确认网络连接正常"
    echo ""
    print_color "💡 如果仓库已存在内容，可以尝试:" $BLUE
    echo "   git pull origin main --allow-unrelated-histories"
    echo "   git push -u origin main"
    exit 1
fi

echo ""
print_color "🏷️ 创建版本标签 (可选):" $BLUE
read -p "是否要创建v1.0.0标签? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git tag -a v1.0.0 -m "🎉 CoachTeacher v1.0.0 - Initial Release

✨ Features:
- 微信登录与角色管理 (教练/学员)
- 智能课程排期管理系统  
- 训练数据分析与报告生成
- 实时沟通与课程确认
- Docker容器化部署
- Prometheus + Grafana 监控

🏗️ Tech Stack:
- Frontend: WeChat MiniProgram
- Backend: Python + FastAPI  
- Database: Supabase (PostgreSQL)
- Cache: Redis
- Proxy: Nginx
- Monitoring: Prometheus + Grafana

📦 Ready for production deployment!"

    if git push origin v1.0.0; then
        print_color "✅ 版本标签v1.0.0已创建并推送" $GREEN
        print_color "💡 现在可以在GitHub上创建Release了" $BLUE
    else
        print_color "⚠️  标签推送失败，但代码已成功推送" $YELLOW
    fi
fi

echo ""
print_color "🎊 完成! 感谢使用CoachTeacher!" $GREEN
