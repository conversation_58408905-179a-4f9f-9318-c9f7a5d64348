/* app.wxss - 全局样式 */

/* 全局变量 */
page {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-disabled: #999999;
  --border-color: #d9d9d9;
  --background-color: #f5f5f5;
  --white: #ffffff;
  
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
}

/* 通用样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.card {
  background: var(--white);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-disabled {
  color: var(--text-disabled);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: 24rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-base {
  font-size: 32rpx;
}

.text-lg {
  font-size: 36rpx;
}

.text-xl {
  font-size: 40rpx;
}

.text-2xl {
  font-size: 48rpx;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #40a9ff;
}

.btn-success {
  background-color: var(--success-color);
  color: var(--white);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--white);
}

.btn-error {
  background-color: var(--error-color);
  color: var(--white);
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 28rpx 48rpx;
  font-size: 36rpx;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  background-color: var(--text-disabled);
  color: var(--white);
  cursor: not-allowed;
}

/* 间距 */
.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-3 { margin-top: 24rpx; }
.mt-4 { margin-top: 32rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }

/* 状态样式 */
.status-pending {
  color: var(--warning-color);
  background-color: #fff7e6;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.status-confirmed {
  color: var(--success-color);
  background-color: #f6ffed;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.status-cancelled {
  color: var(--error-color);
  background-color: #fff2f0;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.status-completed {
  color: var(--text-secondary);
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}
