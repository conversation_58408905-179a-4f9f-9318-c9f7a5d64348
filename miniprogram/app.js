// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:8000/api/v1', // 开发环境API地址
    userRole: null // 'coach' 或 'student'
  },

  onLaunch() {
    // 小程序启动时执行
    console.log('CoachTeacher小程序启动')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  },

  onShow() {
    // 小程序显示时执行
    console.log('CoachTeacher小程序显示')
  },

  onHide() {
    // 小程序隐藏时执行
    console.log('CoachTeacher小程序隐藏')
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
      this.globalData.userRole = userInfo.role
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 获取用户信息
            wx.getUserProfile({
              desc: '用于完善用户资料',
              success: (userRes) => {
                // 发送登录请求到后端
                this.sendLoginRequest(res.code, userRes.userInfo)
                  .then(resolve)
                  .catch(reject)
              },
              fail: reject
            })
          } else {
            reject(new Error('获取登录code失败'))
          }
        },
        fail: reject
      })
    })
  },

  // 发送登录请求
  sendLoginRequest(code, userInfo) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.baseUrl}/auth/login`,
        method: 'POST',
        data: {
          code: code,
          user_info: userInfo
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const { access_token, user } = res.data
            
            // 保存登录信息
            wx.setStorageSync('token', access_token)
            wx.setStorageSync('userInfo', user)
            
            this.globalData.token = access_token
            this.globalData.userInfo = user
            this.globalData.userRole = user.role
            
            resolve(res.data)
          } else {
            reject(new Error('登录失败'))
          }
        },
        fail: reject
      })
    })
  },

  // 退出登录
  logout() {
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    
    this.globalData.token = null
    this.globalData.userInfo = null
    this.globalData.userRole = null
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 通用请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.globalData.token ? `Bearer ${this.globalData.token}` : '',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // token过期，重新登录
            this.logout()
            reject(new Error('登录已过期'))
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        },
        fail: reject
      })
    })
  }
})
