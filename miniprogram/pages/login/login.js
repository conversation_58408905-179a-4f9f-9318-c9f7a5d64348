// pages/login/login.js
const app = getApp()

Page({
  data: {
    selectedRole: '', // 选择的角色：coach 或 student
    isLoading: false, // 是否正在登录
    agreed: false, // 是否同意协议
    showAgreementModal: false, // 显示用户协议弹窗
    showPrivacyModal: false // 显示隐私政策弹窗
  },

  onLoad(options) {
    console.log('登录页面加载')
    
    // 检查是否已经登录
    if (app.globalData.token && app.globalData.userInfo) {
      this.redirectToHome()
    }
  },

  onShow() {
    console.log('登录页面显示')
  },

  // 选择角色
  selectRole(e) {
    const role = e.currentTarget.dataset.role
    this.setData({
      selectedRole: role
    })
    console.log('选择角色:', role)
  },

  // 协议勾选变化
  onAgreementChange(e) {
    const agreed = e.detail.value.includes('agree')
    this.setData({
      agreed: agreed
    })
  },

  // 显示用户协议
  showUserAgreement() {
    this.setData({
      showAgreementModal: true
    })
  },

  // 隐藏用户协议
  hideAgreementModal() {
    this.setData({
      showAgreementModal: false
    })
  },

  // 显示隐私政策
  showPrivacyPolicy() {
    this.setData({
      showPrivacyModal: true
    })
  },

  // 隐藏隐私政策
  hidePrivacyModal() {
    this.setData({
      showPrivacyModal: false
    })
  },

  // 处理登录
  async handleLogin() {
    const { selectedRole, agreed, isLoading } = this.data
    
    // 验证条件
    if (!selectedRole) {
      wx.showToast({
        title: '请选择身份',
        icon: 'none'
      })
      return
    }

    if (!agreed) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    if (isLoading) {
      return
    }

    this.setData({ isLoading: true })

    try {
      // 获取微信登录code
      const loginRes = await this.getWxLoginCode()
      
      // 获取用户信息
      const userInfo = await this.getUserProfile()
      
      // 发送登录请求
      const loginData = await this.sendLoginRequest(loginRes.code, userInfo, selectedRole)
      
      // 登录成功
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        this.redirectToHome()
      }, 1500)

    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 获取微信登录code
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => resolve(res.userInfo),
        fail: reject
      })
    })
  },

  // 发送登录请求
  sendLoginRequest(code, userInfo, role) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.baseUrl}/auth/login`,
        method: 'POST',
        data: {
          code: code,
          user_info: {
            ...userInfo,
            role: role
          }
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const { access_token, user, expires_in } = res.data
            
            // 保存登录信息
            wx.setStorageSync('token', access_token)
            wx.setStorageSync('userInfo', user)
            
            app.globalData.token = access_token
            app.globalData.userInfo = user
            app.globalData.userRole = user.role
            
            resolve(res.data)
          } else {
            reject(new Error(res.data.message || '登录失败'))
          }
        },
        fail: (error) => {
          reject(new Error('网络请求失败'))
        }
      })
    })
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  }
})
