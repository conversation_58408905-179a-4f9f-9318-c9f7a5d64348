/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 角色选择 */
.role-selector {
  margin-bottom: 60rpx;
}

.form-label {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
}

.role-options {
  display: flex;
  gap: 20rpx;
}

.role-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  transition: all 0.3s;
  cursor: pointer;
}

.role-option.active {
  border-color: #1890ff;
  background-color: #f0f8ff;
}

.role-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
}

.role-text {
  font-size: 28rpx;
  color: #666666;
}

.role-option.active .role-text {
  color: #1890ff;
  font-weight: 600;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  transition: all 0.3s;
}

.login-btn:not(.disabled):active {
  transform: scale(0.98);
}

.login-btn.disabled {
  background: #d9d9d9;
  color: #ffffff;
}

.loading-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 用户协议 */
.agreement {
  margin-top: 20rpx;
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
}

.agreement-text {
  margin-left: 10rpx;
}

.link {
  color: #1890ff;
  text-decoration: underline;
}

/* 功能介绍 */
.features {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
}

.feature-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.feature-text {
  font-size: 24rpx;
  text-align: center;
}

/* 弹窗内容 */
.modal-content {
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}
