<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- Logo区域 -->
  <view class="logo-section">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">Coach<PERSON><PERSON>er</text>
    <text class="app-desc">专业的教练学员管理平台</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 角色选择 -->
    <view class="role-selector">
      <text class="form-label">选择身份</text>
      <view class="role-options">
        <view 
          class="role-option {{selectedRole === 'coach' ? 'active' : ''}}"
          bindtap="selectRole"
          data-role="coach"
        >
          <image class="role-icon" src="/images/coach-icon.png"></image>
          <text class="role-text">我是教练</text>
        </view>
        <view 
          class="role-option {{selectedRole === 'student' ? 'active' : ''}}"
          bindtap="selectRole"
          data-role="student"
        >
          <image class="role-icon" src="/images/student-icon.png"></image>
          <text class="role-text">我是学员</text>
        </view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <button 
      class="login-btn {{!selectedRole ? 'disabled' : ''}}"
      disabled="{{!selectedRole || isLoading}}"
      bindtap="handleLogin"
    >
      <image wx:if="{{isLoading}}" class="loading-icon" src="/images/loading.gif"></image>
      {{isLoading ? '登录中...' : '微信一键登录'}}
    </button>

    <!-- 用户协议 -->
    <view class="agreement">
      <checkbox-group bindchange="onAgreementChange">
        <label class="agreement-item">
          <checkbox value="agree" checked="{{agreed}}" />
          <text class="agreement-text">
            我已阅读并同意
            <text class="link" bindtap="showUserAgreement">《用户协议》</text>
            和
            <text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text>
          </text>
        </label>
      </checkbox-group>
    </view>
  </view>

  <!-- 功能介绍 -->
  <view class="features">
    <view class="feature-item">
      <image class="feature-icon" src="/images/schedule-icon.png"></image>
      <text class="feature-text">智能排课管理</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/chat-icon.png"></image>
      <text class="feature-text">实时沟通交流</text>
    </view>
    <view class="feature-item">
      <image class="feature-icon" src="/images/analysis-icon.png"></image>
      <text class="feature-text">专业数据分析</text>
    </view>
  </view>
</view>

<!-- 用户协议弹窗 -->
<modal 
  title="用户协议" 
  hidden="{{!showAgreementModal}}" 
  bindconfirm="hideAgreementModal"
  bindcancel="hideAgreementModal"
>
  <view class="modal-content">
    <text>这里是用户协议的详细内容...</text>
  </view>
</modal>

<!-- 隐私政策弹窗 -->
<modal 
  title="隐私政策" 
  hidden="{{!showPrivacyModal}}" 
  bindconfirm="hidePrivacyModal"
  bindcancel="hidePrivacyModal"
>
  <view class="modal-content">
    <text>这里是隐私政策的详细内容...</text>
  </view>
</modal>
