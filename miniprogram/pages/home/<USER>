<!--pages/home/<USER>
<view class="home-container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="username">{{userInfo.nickname || '用户'}}</text>
        <text class="user-role">{{userInfo.role === 'coach' ? '教练' : '学员'}}</text>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-number">{{stats.totalSchedules || 0}}</text>
        <text class="stat-label">{{userInfo.role === 'coach' ? '总课程' : '已上课程'}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.thisWeek || 0}}</text>
        <text class="stat-label">本周课程</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <text class="section-title">快捷操作</text>
    <view class="action-grid">
      <!-- 教练端操作 -->
      <block wx:if="{{userInfo.role === 'coach'}}">
        <view class="action-item" bindtap="navigateToCreateSchedule">
          <image class="action-icon" src="/images/add-schedule.png"></image>
          <text class="action-text">创建课程</text>
        </view>
        <view class="action-item" bindtap="navigateToStudents">
          <image class="action-icon" src="/images/students.png"></image>
          <text class="action-text">学员管理</text>
        </view>
        <view class="action-item" bindtap="navigateToTrainingRecord">
          <image class="action-icon" src="/images/training.png"></image>
          <text class="action-text">训练记录</text>
        </view>
        <view class="action-item" bindtap="navigateToAnalysis">
          <image class="action-icon" src="/images/analysis.png"></image>
          <text class="action-text">数据分析</text>
        </view>
      </block>
      
      <!-- 学员端操作 -->
      <block wx:else>
        <view class="action-item" bindtap="navigateToSchedules">
          <image class="action-icon" src="/images/schedule.png"></image>
          <text class="action-text">我的课程</text>
        </view>
        <view class="action-item" bindtap="navigateToFitnessReport">
          <image class="action-icon" src="/images/fitness.png"></image>
          <text class="action-text">体测报告</text>
        </view>
        <view class="action-item" bindtap="navigateToAnalysis">
          <image class="action-icon" src="/images/analysis.png"></image>
          <text class="action-text">我的分析</text>
        </view>
        <view class="action-item" bindtap="navigateToCoaches">
          <image class="action-icon" src="/images/coach.png"></image>
          <text class="action-text">找教练</text>
        </view>
      </block>
    </view>
  </view>

  <!-- 今日课程 -->
  <view class="today-schedules">
    <view class="section-header">
      <text class="section-title">今日课程</text>
      <text class="view-all" bindtap="navigateToSchedules">查看全部</text>
    </view>
    
    <block wx:if="{{todaySchedules.length > 0}}">
      <view class="schedule-list">
        <view 
          class="schedule-item" 
          wx:for="{{todaySchedules}}" 
          wx:key="id"
          bindtap="navigateToScheduleDetail"
          data-id="{{item.id}}"
        >
          <view class="schedule-time">
            <text class="time">{{item.start_time_formatted}}</text>
            <text class="duration">{{item.duration}}分钟</text>
          </view>
          <view class="schedule-info">
            <text class="schedule-title">{{item.title}}</text>
            <text class="schedule-partner">
              {{userInfo.role === 'coach' ? '学员: ' + item.student_name : '教练: ' + item.coach_name}}
            </text>
            <text class="schedule-location" wx:if="{{item.location}}">📍 {{item.location}}</text>
          </view>
          <view class="schedule-status status-{{item.status}}">
            {{item.status_text}}
          </view>
        </view>
      </view>
    </block>
    
    <block wx:else>
      <view class="empty-state">
        <image class="empty-icon" src="/images/empty-schedule.png"></image>
        <text class="empty-text">今日暂无课程安排</text>
        <button 
          class="btn btn-primary btn-small" 
          wx:if="{{userInfo.role === 'coach'}}"
          bindtap="navigateToCreateSchedule"
        >
          创建课程
        </button>
      </view>
    </block>
  </view>

  <!-- 最新通知 -->
  <view class="notifications" wx:if="{{notifications.length > 0}}">
    <view class="section-header">
      <text class="section-title">最新通知</text>
      <text class="view-all" bindtap="navigateToNotifications">查看全部</text>
    </view>
    
    <view class="notification-list">
      <view 
        class="notification-item {{!item.is_read ? 'unread' : ''}}" 
        wx:for="{{notifications}}" 
        wx:key="id"
        bindtap="handleNotificationTap"
        data-id="{{item.id}}"
      >
        <view class="notification-content">
          <text class="notification-title">{{item.title}}</text>
          <text class="notification-text">{{item.content}}</text>
          <text class="notification-time">{{item.created_at_formatted}}</text>
        </view>
        <view class="notification-indicator" wx:if="{{!item.is_read}}"></view>
      </view>
    </view>
  </view>

  <!-- 数据概览 -->
  <view class="data-overview" wx:if="{{userInfo.role === 'student'}}">
    <text class="section-title">本月数据</text>
    <view class="overview-grid">
      <view class="overview-item">
        <text class="overview-number">{{monthlyData.sessions || 0}}</text>
        <text class="overview-label">训练次数</text>
      </view>
      <view class="overview-item">
        <text class="overview-number">{{monthlyData.duration || 0}}</text>
        <text class="overview-label">训练时长(分钟)</text>
      </view>
      <view class="overview-item">
        <text class="overview-number">{{monthlyData.calories || 0}}</text>
        <text class="overview-label">消耗卡路里</text>
      </view>
      <view class="overview-item">
        <text class="overview-number">{{monthlyData.intensity || 0}}</text>
        <text class="overview-label">平均强度</text>
      </view>
    </view>
  </view>
</view>
