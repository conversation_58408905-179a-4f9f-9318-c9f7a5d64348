/* pages/home/<USER>/

.home-container {
  padding: 0;
  background-color: var(--background-color);
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: white;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-role {
  display: block;
  font-size: 28rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  width: fit-content;
}

.user-stats {
  display: flex;
  gap: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 快捷操作 */
.quick-actions {
  padding: 30rpx;
  background: white;
  margin-bottom: 20rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 30rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s;
}

.action-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: var(--text-color);
  text-align: center;
}

/* 今日课程 */
.today-schedules {
  padding: 30rpx;
  background: white;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.view-all {
  font-size: 28rpx;
  color: var(--primary-color);
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.schedule-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid var(--primary-color);
}

.schedule-time {
  margin-right: 20rpx;
  text-align: center;
  min-width: 120rpx;
}

.time {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4rpx;
}

.duration {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.schedule-info {
  flex: 1;
  margin-right: 20rpx;
}

.schedule-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.schedule-partner {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 4rpx;
}

.schedule-location {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.schedule-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-confirmed {
  background: #f6ffed;
  color: #52c41a;
}

.status-cancelled {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-completed {
  background: #f0f0f0;
  color: #8c8c8c;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 30rpx;
}

/* 通知 */
.notifications {
  padding: 30rpx;
  background: white;
  margin-bottom: 20rpx;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notification-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  position: relative;
}

.notification-item.unread {
  background: #e6f7ff;
  border-left: 4rpx solid var(--primary-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.notification-text {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.notification-time {
  display: block;
  font-size: 24rpx;
  color: var(--text-disabled);
}

.notification-indicator {
  width: 12rpx;
  height: 12rpx;
  background: var(--primary-color);
  border-radius: 50%;
  margin-left: 16rpx;
}

/* 数据概览 */
.data-overview {
  padding: 30rpx;
  background: white;
  margin-bottom: 20rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.overview-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.overview-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.overview-label {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
}
