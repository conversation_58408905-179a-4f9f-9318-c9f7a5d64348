// pages/home/<USER>
const app = getApp()

Page({
  data: {
    userInfo: {},
    stats: {},
    todaySchedules: [],
    notifications: [],
    monthlyData: {},
    loading: false
  },

  onLoad() {
    console.log('首页加载')
    this.checkLoginStatus()
  },

  onShow() {
    console.log('首页显示')
    if (app.globalData.userInfo) {
      this.loadPageData()
    }
  },

  onPullDownRefresh() {
    this.loadPageData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    if (!app.globalData.token || !app.globalData.userInfo) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
    
    this.setData({
      userInfo: app.globalData.userInfo
    })
    
    this.loadPageData()
  },

  // 加载页面数据
  async loadPageData() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadUserStats(),
        this.loadTodaySchedules(),
        this.loadNotifications(),
        this.loadMonthlyData()
      ])
    } catch (error) {
      console.error('加载页面数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载用户统计数据
  async loadUserStats() {
    try {
      const today = new Date()
      const weekStart = new Date(today.setDate(today.getDate() - today.getDay()))
      const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6))
      
      // 获取总课程数和本周课程数
      const [totalRes, weekRes] = await Promise.all([
        app.request({
          url: '/schedules',
          method: 'GET'
        }),
        app.request({
          url: '/schedules',
          method: 'GET',
          data: {
            start_date: weekStart.toISOString().split('T')[0],
            end_date: weekEnd.toISOString().split('T')[0]
          }
        })
      ])
      
      this.setData({
        stats: {
          totalSchedules: totalRes.length || 0,
          thisWeek: weekRes.length || 0
        }
      })
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  },

  // 加载今日课程
  async loadTodaySchedules() {
    try {
      const today = new Date().toISOString().split('T')[0]
      
      const response = await app.request({
        url: '/schedules',
        method: 'GET',
        data: {
          start_date: today,
          end_date: today
        }
      })
      
      const schedules = response.map(schedule => ({
        ...schedule,
        start_time_formatted: this.formatTime(schedule.start_time),
        duration: this.calculateDuration(schedule.start_time, schedule.end_time),
        status_text: this.getStatusText(schedule.status)
      }))
      
      this.setData({
        todaySchedules: schedules
      })
    } catch (error) {
      console.error('加载今日课程失败:', error)
    }
  },

  // 加载通知
  async loadNotifications() {
    try {
      const response = await app.request({
        url: '/notifications',
        method: 'GET',
        data: {
          page: 1,
          size: 5
        }
      })
      
      const notifications = response.map(notification => ({
        ...notification,
        created_at_formatted: this.formatDateTime(notification.created_at)
      }))
      
      this.setData({
        notifications: notifications
      })
    } catch (error) {
      console.error('加载通知失败:', error)
    }
  },

  // 加载月度数据（仅学员）
  async loadMonthlyData() {
    if (this.data.userInfo.role !== 'student') return
    
    try {
      const today = new Date()
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0)
      
      const response = await app.request({
        url: `/analysis/report/${this.data.userInfo.id}`,
        method: 'GET',
        data: {
          start_date: monthStart.toISOString().split('T')[0],
          end_date: monthEnd.toISOString().split('T')[0]
        }
      })
      
      this.setData({
        monthlyData: {
          sessions: response.total_sessions,
          duration: response.total_duration,
          calories: response.total_calories,
          intensity: response.average_intensity
        }
      })
    } catch (error) {
      console.error('加载月度数据失败:', error)
    }
  },

  // 导航方法
  navigateToCreateSchedule() {
    wx.navigateTo({
      url: '/pages/schedule/create/create'
    })
  },

  navigateToSchedules() {
    wx.switchTab({
      url: '/pages/schedule/schedule'
    })
  },

  navigateToAnalysis() {
    wx.switchTab({
      url: '/pages/analysis/analysis'
    })
  },

  navigateToScheduleDetail(e) {
    const scheduleId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/schedule/detail/detail?id=${scheduleId}`
    })
  },

  navigateToStudents() {
    wx.navigateTo({
      url: '/pages/students/students'
    })
  },

  navigateToCoaches() {
    wx.navigateTo({
      url: '/pages/coaches/coaches'
    })
  },

  navigateToFitnessReport() {
    wx.navigateTo({
      url: '/pages/fitness/fitness'
    })
  },

  navigateToTrainingRecord() {
    wx.navigateTo({
      url: '/pages/training/training'
    })
  },

  navigateToNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    })
  },

  // 处理通知点击
  async handleNotificationTap(e) {
    const notificationId = e.currentTarget.dataset.id
    
    try {
      // 标记为已读
      await app.request({
        url: `/notifications/${notificationId}/read`,
        method: 'POST'
      })
      
      // 更新本地状态
      const notifications = this.data.notifications.map(item => {
        if (item.id === notificationId) {
          return { ...item, is_read: true }
        }
        return item
      })
      
      this.setData({ notifications })
      
      // TODO: 根据通知类型跳转到相应页面
      
    } catch (error) {
      console.error('处理通知失败:', error)
    }
  },

  // 工具方法
  formatTime(timeString) {
    const date = new Date(timeString)
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  },

  formatDateTime(timeString) {
    const date = new Date(timeString)
    const now = new Date()
    const diff = now - date
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 24小时内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  },

  calculateDuration(startTime, endTime) {
    const start = new Date(startTime)
    const end = new Date(endTime)
    return Math.round((end - start) / 60000) // 分钟
  },

  getStatusText(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'cancelled': '已取消',
      'completed': '已完成'
    }
    return statusMap[status] || status
  }
})
