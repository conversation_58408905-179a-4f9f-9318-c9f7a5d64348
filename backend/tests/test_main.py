"""
主要API端点测试
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from main import app

client = TestClient(app)

def test_root_endpoint():
    """测试根路径端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "CoachTeacher API is running"
    assert data["version"] == "1.0.0"
    assert data["docs"] == "/docs"

def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

@patch('app.core.database.get_db')
def test_login_endpoint_structure(mock_get_db):
    """测试登录端点结构（不进行实际微信API调用）"""
    # 模拟数据库客户端
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # 测试缺少必要参数的情况
    response = client.post("/api/v1/auth/login", json={})
    assert response.status_code == 422  # 验证错误
    
    # 测试请求结构
    login_data = {
        "code": "test_code",
        "user_info": {
            "nickName": "测试用户",
            "avatarUrl": "https://example.com/avatar.jpg",
            "role": "student"
        }
    }
    
    # 由于需要实际的微信API调用，这里只测试请求结构
    response = client.post("/api/v1/auth/login", json=login_data)
    # 预期会因为微信API调用失败而返回500错误
    assert response.status_code in [400, 500]

def test_api_docs_accessible():
    """测试API文档是否可访问"""
    response = client.get("/docs")
    assert response.status_code == 200
    
    response = client.get("/redoc")
    assert response.status_code == 200

def test_cors_headers():
    """测试CORS头部设置"""
    response = client.options("/api/v1/schedules")
    assert response.status_code == 200
    # 检查CORS头部是否存在
    assert "access-control-allow-origin" in response.headers

@pytest.mark.asyncio
async def test_database_initialization():
    """测试数据库初始化"""
    from app.core.database import init_db
    
    # 测试在没有配置的情况下不会抛出异常
    try:
        await init_db()
    except Exception as e:
        # 预期会有配置错误，但不应该是代码错误
        assert "配置" in str(e) or "连接" in str(e)

def test_user_model_validation():
    """测试用户模型验证"""
    from app.models.user import UserCreate, UserRole
    
    # 测试有效的用户创建数据
    valid_user_data = {
        "wechat_openid": "test_openid",
        "nickname": "测试用户",
        "role": UserRole.STUDENT
    }
    
    user = UserCreate(**valid_user_data)
    assert user.nickname == "测试用户"
    assert user.role == UserRole.STUDENT
    
    # 测试无效的角色
    with pytest.raises(ValueError):
        UserCreate(
            wechat_openid="test_openid",
            nickname="测试用户",
            role="invalid_role"
        )

def test_schedule_model_validation():
    """测试课程排期模型验证"""
    from app.models.schedule import ScheduleCreate, ScheduleType, ScheduleStatus
    from datetime import datetime, timedelta
    
    now = datetime.now()
    
    # 测试有效的排期创建数据
    valid_schedule_data = {
        "coach_id": 1,
        "student_id": 2,
        "title": "力量训练",
        "schedule_type": ScheduleType.PERSONAL,
        "start_time": now,
        "end_time": now + timedelta(hours=1)
    }
    
    schedule = ScheduleCreate(**valid_schedule_data)
    assert schedule.title == "力量训练"
    assert schedule.schedule_type == ScheduleType.PERSONAL
    
    # 测试无效的课程类型
    with pytest.raises(ValueError):
        ScheduleCreate(
            coach_id=1,
            student_id=2,
            title="测试课程",
            schedule_type="invalid_type",
            start_time=now,
            end_time=now + timedelta(hours=1)
        )

def test_config_loading():
    """测试配置加载"""
    from app.core.config import settings
    
    # 测试默认配置值
    assert settings.APP_NAME == "CoachTeacher"
    assert settings.VERSION == "1.0.0"
    assert settings.ALGORITHM == "HS256"
    assert settings.ACCESS_TOKEN_EXPIRE_MINUTES == 30

if __name__ == "__main__":
    pytest.main([__file__])
