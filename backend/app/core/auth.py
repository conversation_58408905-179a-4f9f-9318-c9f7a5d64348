"""
认证和授权相关功能
"""
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
from typing import Optional

from app.core.config import settings
from app.core.database import get_db
from app.models.user import User

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """
    获取当前认证用户
    """
    try:
        # 解码JWT token
        payload = jwt.decode(
            credentials.credentials, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        
        user_id = payload.get('user_id')
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 从数据库获取用户信息
        db = get_db()
        result = db.table('users').select('*').eq('id', user_id).eq('is_active', True).execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return User(**result.data[0])
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证令牌已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"认证失败: {str(e)}"
        )

async def get_current_coach(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前教练用户（仅教练可访问）
    """
    if current_user.role != 'coach':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要教练权限"
        )
    return current_user

async def get_current_student(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前学员用户（仅学员可访问）
    """
    if current_user.role != 'student':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要学员权限"
        )
    return current_user

def verify_user_access(target_user_id: int, current_user: User) -> bool:
    """
    验证用户是否有权限访问目标用户的数据
    """
    # 用户可以访问自己的数据
    if current_user.id == target_user_id:
        return True
    
    # 教练可以访问其学员的数据（需要进一步实现师生关系检查）
    if current_user.role == 'coach':
        # TODO: 检查是否为该教练的学员
        return True
    
    return False
