"""
数据库连接和初始化
"""
from supabase import create_client, Client
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Supabase客户端
supabase: Client = None

async def init_db():
    """初始化数据库连接"""
    global supabase
    
    if not settings.SUPABASE_URL or not settings.SUPABASE_KEY:
        logger.warning("Supabase配置未完整设置")
        return
    
    try:
        supabase = create_client(
            settings.SUPABASE_URL,
            settings.SUPABASE_KEY
        )
        logger.info("Supabase数据库连接成功")
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def get_db() -> Client:
    """获取数据库客户端"""
    if supabase is None:
        raise Exception("数据库未初始化")
    return supabase
