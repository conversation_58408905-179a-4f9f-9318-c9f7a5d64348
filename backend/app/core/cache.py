"""
Redis缓存模块
"""
import json
import logging
from typing import Optional, Any
import aioredis
from app.core.config import settings

logger = logging.getLogger(__name__)

class RedisCache:
    """Redis缓存管理器"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        
    async def connect(self):
        """连接Redis"""
        try:
            redis_url = getattr(settings, 'REDIS_URL', 'redis://redis:6379')
            self.redis = aioredis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=20
            )
            # 测试连接
            await self.redis.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.warning(f"Redis连接失败: {e}")
            self.redis = None
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis:
            await self.redis.close()
            logger.info("Redis连接已关闭")
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.redis:
            return None
            
        try:
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """设置缓存值"""
        if not self.redis:
            return False
            
        try:
            json_value = json.dumps(value, ensure_ascii=False, default=str)
            await self.redis.setex(key, expire, json_value)
            return True
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self.redis:
            return False
            
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self.redis:
            return False
            
        try:
            return await self.redis.exists(key) > 0
        except Exception as e:
            logger.error(f"检查缓存失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置缓存过期时间"""
        if not self.redis:
            return False
            
        try:
            await self.redis.expire(key, seconds)
            return True
        except Exception as e:
            logger.error(f"设置过期时间失败 {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        if not self.redis:
            return 0
            
        try:
            keys = await self.redis.keys(pattern)
            if keys:
                return await self.redis.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"清除缓存失败 {pattern}: {e}")
            return 0

# 全局缓存实例
cache = RedisCache()

# 缓存装饰器
def cached(key_prefix: str, expire: int = 3600):
    """缓存装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await cache.set(cache_key, result, expire)
            return result
        
        return wrapper
    return decorator

# 缓存键生成器
class CacheKeys:
    """缓存键管理"""
    
    @staticmethod
    def user_info(user_id: int) -> str:
        return f"user:info:{user_id}"
    
    @staticmethod
    def user_schedules(user_id: int, date: str = None) -> str:
        if date:
            return f"user:schedules:{user_id}:{date}"
        return f"user:schedules:{user_id}"
    
    @staticmethod
    def schedule_detail(schedule_id: int) -> str:
        return f"schedule:detail:{schedule_id}"
    
    @staticmethod
    def analysis_report(user_id: int, start_date: str, end_date: str) -> str:
        return f"analysis:report:{user_id}:{start_date}:{end_date}"
    
    @staticmethod
    def fitness_reports(user_id: int) -> str:
        return f"fitness:reports:{user_id}"
