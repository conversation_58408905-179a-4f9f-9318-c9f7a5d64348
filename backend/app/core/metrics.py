"""
Prometheus监控指标
"""
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from fastapi import Request, Response
import time
import logging

logger = logging.getLogger(__name__)

# 定义监控指标
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_CONNECTIONS = Gauge(
    'active_connections',
    'Number of active connections'
)

DATABASE_QUERIES = Counter(
    'database_queries_total',
    'Total database queries',
    ['operation', 'table']
)

CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'result']
)

USER_REGISTRATIONS = Counter(
    'user_registrations_total',
    'Total user registrations',
    ['role']
)

SCHEDULE_OPERATIONS = Counter(
    'schedule_operations_total',
    'Total schedule operations',
    ['operation', 'status']
)

class MetricsMiddleware:
    """监控指标中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        start_time = time.time()
        
        # 增加活跃连接数
        ACTIVE_CONNECTIONS.inc()
        
        try:
            # 处理请求
            response = Response()
            
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    response.status_code = message["status"]
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
            
            # 记录指标
            duration = time.time() - start_time
            method = request.method
            path = request.url.path
            status_code = getattr(response, 'status_code', 200)
            
            REQUEST_COUNT.labels(
                method=method,
                endpoint=path,
                status_code=status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=path
            ).observe(duration)
            
        except Exception as e:
            logger.error(f"监控中间件错误: {e}")
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status_code=500
            ).inc()
            raise
        finally:
            # 减少活跃连接数
            ACTIVE_CONNECTIONS.dec()

def record_database_query(operation: str, table: str):
    """记录数据库查询"""
    DATABASE_QUERIES.labels(operation=operation, table=table).inc()

def record_cache_operation(operation: str, result: str):
    """记录缓存操作"""
    CACHE_OPERATIONS.labels(operation=operation, result=result).inc()

def record_user_registration(role: str):
    """记录用户注册"""
    USER_REGISTRATIONS.labels(role=role).inc()

def record_schedule_operation(operation: str, status: str):
    """记录排期操作"""
    SCHEDULE_OPERATIONS.labels(operation=operation, status=status).inc()

async def get_metrics():
    """获取监控指标"""
    return generate_latest()
