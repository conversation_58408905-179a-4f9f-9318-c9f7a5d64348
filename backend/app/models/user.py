"""
用户相关数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Literal
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    """用户角色枚举"""
    COACH = "coach"
    STUDENT = "student"

class UserBase(BaseModel):
    """用户基础模型"""
    wechat_openid: str = Field(..., description="微信OpenID")
    nickname: str = Field(..., description="用户昵称")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    phone: Optional[str] = Field(None, description="手机号")
    role: UserRole = Field(..., description="用户角色")

class UserCreate(UserBase):
    """创建用户模型"""
    pass

class UserUpdate(BaseModel):
    """更新用户模型"""
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    phone: Optional[str] = None

class User(UserBase):
    """用户完整模型"""
    id: int = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    is_active: bool = Field(True, description="是否激活")
    
    class Config:
        from_attributes = True

class CoachProfile(BaseModel):
    """教练档案模型"""
    user_id: int = Field(..., description="用户ID")
    specialties: Optional[str] = Field(None, description="专业特长")
    experience_years: Optional[int] = Field(None, description="从业年限")
    certification: Optional[str] = Field(None, description="资质证书")
    bio: Optional[str] = Field(None, description="个人简介")
    hourly_rate: Optional[float] = Field(None, description="课时费")

class StudentProfile(BaseModel):
    """学员档案模型"""
    user_id: int = Field(..., description="用户ID")
    age: Optional[int] = Field(None, description="年龄")
    gender: Optional[Literal["male", "female"]] = Field(None, description="性别")
    fitness_goal: Optional[str] = Field(None, description="健身目标")
    health_conditions: Optional[str] = Field(None, description="健康状况")
    emergency_contact: Optional[str] = Field(None, description="紧急联系人")

class LoginRequest(BaseModel):
    """登录请求模型"""
    code: str = Field(..., description="微信登录code")
    user_info: dict = Field(..., description="用户信息")

class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    user: User = Field(..., description="用户信息")
    expires_in: int = Field(..., description="过期时间(秒)")
