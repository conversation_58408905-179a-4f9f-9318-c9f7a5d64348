"""
课程排期相关数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class ScheduleStatus(str, Enum):
    """课程状态枚举"""
    PENDING = "pending"      # 待确认
    CONFIRMED = "confirmed"  # 已确认
    CANCELLED = "cancelled"  # 已取消
    COMPLETED = "completed"  # 已完成

class ScheduleType(str, Enum):
    """课程类型枚举"""
    PERSONAL = "personal"    # 私教
    GROUP = "group"         # 团课
    ONLINE = "online"       # 线上课程

class ScheduleBase(BaseModel):
    """课程排期基础模型"""
    coach_id: int = Field(..., description="教练ID")
    student_id: int = Field(..., description="学员ID")
    title: str = Field(..., description="课程标题")
    description: Optional[str] = Field(None, description="课程描述")
    schedule_type: ScheduleType = Field(..., description="课程类型")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    location: Optional[str] = Field(None, description="上课地点")
    price: Optional[float] = Field(None, description="课程费用")

class ScheduleCreate(ScheduleBase):
    """创建课程排期模型"""
    pass

class ScheduleUpdate(BaseModel):
    """更新课程排期模型"""
    title: Optional[str] = None
    description: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    price: Optional[float] = None
    status: Optional[ScheduleStatus] = None

class Schedule(ScheduleBase):
    """课程排期完整模型"""
    id: int = Field(..., description="排期ID")
    status: ScheduleStatus = Field(ScheduleStatus.PENDING, description="课程状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 关联信息
    coach_name: Optional[str] = Field(None, description="教练姓名")
    student_name: Optional[str] = Field(None, description="学员姓名")
    
    class Config:
        from_attributes = True

class ScheduleQuery(BaseModel):
    """课程查询模型"""
    coach_id: Optional[int] = None
    student_id: Optional[int] = None
    status: Optional[ScheduleStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=100, description="每页数量")

class ScheduleConfirmRequest(BaseModel):
    """课程确认请求模型"""
    schedule_id: int = Field(..., description="排期ID")
    action: Literal["confirm", "cancel"] = Field(..., description="操作类型")
    reason: Optional[str] = Field(None, description="操作原因")

class ScheduleRescheduleRequest(BaseModel):
    """课程改期请求模型"""
    schedule_id: int = Field(..., description="排期ID")
    new_start_time: datetime = Field(..., description="新开始时间")
    new_end_time: datetime = Field(..., description="新结束时间")
    reason: Optional[str] = Field(None, description="改期原因")
