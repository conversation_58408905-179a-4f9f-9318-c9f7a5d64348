"""
认证相关API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer
import httpx
import jwt
from datetime import datetime, timedelta
from typing import Optional

from app.core.config import settings
from app.core.database import get_db
from app.models.user import LoginRequest, LoginResponse, User, UserCreate, UserRole

router = APIRouter()
security = HTTPBearer()

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest):
    """
    微信登录
    """
    try:
        # 1. 验证微信登录code
        wechat_data = await verify_wechat_code(login_data.code)
        
        if not wechat_data.get('openid'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="微信登录验证失败"
            )
        
        openid = wechat_data['openid']
        user_info = login_data.user_info
        
        # 2. 查找或创建用户
        db = get_db()
        
        # 查找现有用户
        existing_user = db.table('users').select('*').eq('wechat_openid', openid).execute()
        
        if existing_user.data:
            # 用户已存在，更新信息
            user_data = existing_user.data[0]
            updated_user = db.table('users').update({
                'nickname': user_info.get('nickName', user_data['nickname']),
                'avatar_url': user_info.get('avatarUrl', user_data['avatar_url']),
                'updated_at': datetime.utcnow().isoformat()
            }).eq('id', user_data['id']).execute()
            
            user = updated_user.data[0]
        else:
            # 创建新用户
            new_user_data = {
                'wechat_openid': openid,
                'nickname': user_info.get('nickName', '用户'),
                'avatar_url': user_info.get('avatarUrl'),
                'role': user_info.get('role', 'student'),
                'is_active': True,
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }
            
            created_user = db.table('users').insert(new_user_data).execute()
            user = created_user.data[0]
            
            # 根据角色创建对应的档案
            if user['role'] == 'coach':
                db.table('coach_profiles').insert({
                    'user_id': user['id'],
                    'created_at': datetime.utcnow().isoformat()
                }).execute()
            else:
                db.table('student_profiles').insert({
                    'user_id': user['id'],
                    'created_at': datetime.utcnow().isoformat()
                }).execute()
        
        # 3. 生成JWT token
        access_token = create_access_token(user['id'])
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=User(**user),
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

async def verify_wechat_code(code: str) -> dict:
    """
    验证微信登录code
    """
    url = "https://api.weixin.qq.com/sns/jscode2session"
    params = {
        'appid': settings.WECHAT_APP_ID,
        'secret': settings.WECHAT_APP_SECRET,
        'js_code': code,
        'grant_type': 'authorization_code'
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=params)
        data = response.json()
        
        if 'errcode' in data:
            raise Exception(f"微信API错误: {data.get('errmsg', '未知错误')}")
        
        return data

def create_access_token(user_id: int) -> str:
    """
    创建访问令牌
    """
    expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    payload = {
        'user_id': user_id,
        'exp': expire,
        'iat': datetime.utcnow()
    }
    
    return jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

@router.post("/refresh")
async def refresh_token(token: str = Depends(security)):
    """
    刷新访问令牌
    """
    try:
        payload = jwt.decode(token.credentials, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id = payload.get('user_id')
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌"
            )
        
        # 生成新的token
        new_token = create_access_token(user_id)
        
        return {
            'access_token': new_token,
            'token_type': 'bearer',
            'expires_in': settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
        
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌已过期"
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌"
        )
