"""
数据分析API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from pydantic import BaseModel

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User

router = APIRouter()

class FitnessReport(BaseModel):
    """体测报告模型"""
    id: int
    student_id: int
    report_date: date
    height: Optional[float] = None
    weight: Optional[float] = None
    body_fat_percentage: Optional[float] = None
    muscle_mass: Optional[float] = None
    bmr: Optional[int] = None
    visceral_fat_level: Optional[int] = None
    bone_mass: Optional[float] = None
    water_percentage: Optional[float] = None
    protein_percentage: Optional[float] = None
    report_data: Optional[Dict[str, Any]] = None
    created_at: datetime

class TrainingRecord(BaseModel):
    """训练记录模型"""
    id: int
    schedule_id: int
    student_id: int
    coach_id: int
    training_date: date
    duration_minutes: Optional[int] = None
    intensity_level: Optional[int] = None
    calories_burned: Optional[int] = None
    exercises: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None
    created_at: datetime

class AnalysisReport(BaseModel):
    """分析报告模型"""
    student_id: int
    student_name: str
    period_start: date
    period_end: date
    total_sessions: int
    total_duration: int
    average_intensity: float
    total_calories: int
    fitness_trend: Dict[str, Any]
    training_summary: Dict[str, Any]

@router.post("/fitness-reports", response_model=FitnessReport)
async def create_fitness_report(
    report_data: FitnessReport,
    current_user: User = Depends(get_current_user)
):
    """
    创建体测报告
    """
    # 权限检查：只有教练可以为学员创建体测报告，学员只能为自己创建
    if current_user.role == 'student' and report_data.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只能为自己创建体测报告"
        )
    
    try:
        db = get_db()
        
        # 验证学员是否存在
        if current_user.role == 'coach':
            student_check = db.table('users').select('id').eq('id', report_data.student_id)\
                            .eq('role', 'student').execute()
            if not student_check.data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="学员不存在"
                )
        
        # 创建体测报告
        new_report = {
            **report_data.dict(exclude={'id', 'created_at'}),
            'report_date': report_data.report_date.isoformat(),
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        result = db.table('fitness_reports').insert(new_report).execute()
        
        if result.data:
            return FitnessReport(**result.data[0])
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建体测报告失败"
        )
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建体测报告失败: {str(e)}"
        )

@router.get("/fitness-reports", response_model=List[FitnessReport])
async def get_fitness_reports(
    student_id: Optional[int] = Query(None, description="学员ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_user)
):
    """
    获取体测报告列表
    """
    try:
        db = get_db()
        
        query = db.table('fitness_reports').select('*')
        
        # 权限控制
        if current_user.role == 'student':
            query = query.eq('student_id', current_user.id)
        elif student_id:
            query = query.eq('student_id', student_id)
        
        # 日期过滤
        if start_date:
            query = query.gte('report_date', start_date.isoformat())
        if end_date:
            query = query.lte('report_date', end_date.isoformat())
        
        query = query.order('report_date', desc=True)
        
        result = query.execute()
        
        return [FitnessReport(**report) for report in result.data]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取体测报告失败: {str(e)}"
        )

@router.post("/training-records", response_model=TrainingRecord)
async def create_training_record(
    record_data: TrainingRecord,
    current_user: User = Depends(get_current_user)
):
    """
    创建训练记录（仅教练可创建）
    """
    if current_user.role != 'coach':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有教练可以创建训练记录"
        )
    
    try:
        db = get_db()
        
        # 验证课程排期是否存在且属于当前教练
        schedule_check = db.table('schedules').select('student_id').eq('id', record_data.schedule_id)\
                         .eq('coach_id', current_user.id).execute()
        
        if not schedule_check.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="课程排期不存在或无权访问"
            )
        
        # 创建训练记录
        new_record = {
            **record_data.dict(exclude={'id', 'created_at'}),
            'coach_id': current_user.id,
            'student_id': schedule_check.data[0]['student_id'],
            'training_date': record_data.training_date.isoformat(),
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        result = db.table('training_records').insert(new_record).execute()
        
        if result.data:
            return TrainingRecord(**result.data[0])
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建训练记录失败"
        )
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建训练记录失败: {str(e)}"
        )

@router.get("/training-records", response_model=List[TrainingRecord])
async def get_training_records(
    student_id: Optional[int] = Query(None, description="学员ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_user)
):
    """
    获取训练记录列表
    """
    try:
        db = get_db()
        
        query = db.table('training_records').select('*')
        
        # 权限控制
        if current_user.role == 'student':
            query = query.eq('student_id', current_user.id)
        elif current_user.role == 'coach':
            query = query.eq('coach_id', current_user.id)
            if student_id:
                query = query.eq('student_id', student_id)
        
        # 日期过滤
        if start_date:
            query = query.gte('training_date', start_date.isoformat())
        if end_date:
            query = query.lte('training_date', end_date.isoformat())
        
        query = query.order('training_date', desc=True)
        
        result = query.execute()
        
        return [TrainingRecord(**record) for record in result.data]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取训练记录失败: {str(e)}"
        )

@router.get("/report/{student_id}", response_model=AnalysisReport)
async def get_analysis_report(
    student_id: int,
    start_date: Optional[date] = Query(None, description="分析开始日期"),
    end_date: Optional[date] = Query(None, description="分析结束日期"),
    current_user: User = Depends(get_current_user)
):
    """
    获取学员分析报告
    """
    # 权限检查
    if current_user.role == 'student' and student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只能查看自己的分析报告"
        )
    
    try:
        db = get_db()
        
        # 设置默认时间范围（最近30天）
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # 获取学员信息
        student_result = db.table('users').select('nickname').eq('id', student_id)\
                         .eq('role', 'student').execute()
        
        if not student_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学员不存在"
            )
        
        student_name = student_result.data[0]['nickname']
        
        # 获取训练记录
        training_records = db.table('training_records').select('*')\
                           .eq('student_id', student_id)\
                           .gte('training_date', start_date.isoformat())\
                           .lte('training_date', end_date.isoformat()).execute()
        
        # 获取体测报告
        fitness_reports = db.table('fitness_reports').select('*')\
                          .eq('student_id', student_id)\
                          .gte('report_date', start_date.isoformat())\
                          .lte('report_date', end_date.isoformat())\
                          .order('report_date', desc=False).execute()
        
        # 计算统计数据
        total_sessions = len(training_records.data)
        total_duration = sum(record.get('duration_minutes', 0) for record in training_records.data)
        total_calories = sum(record.get('calories_burned', 0) for record in training_records.data)
        
        intensity_values = [record.get('intensity_level', 0) for record in training_records.data if record.get('intensity_level')]
        average_intensity = sum(intensity_values) / len(intensity_values) if intensity_values else 0
        
        # 分析体测趋势
        fitness_trend = {}
        if fitness_reports.data:
            latest_report = fitness_reports.data[-1]
            earliest_report = fitness_reports.data[0]
            
            fitness_trend = {
                'weight_change': (latest_report.get('weight', 0) - earliest_report.get('weight', 0)) if latest_report.get('weight') and earliest_report.get('weight') else 0,
                'body_fat_change': (latest_report.get('body_fat_percentage', 0) - earliest_report.get('body_fat_percentage', 0)) if latest_report.get('body_fat_percentage') and earliest_report.get('body_fat_percentage') else 0,
                'muscle_mass_change': (latest_report.get('muscle_mass', 0) - earliest_report.get('muscle_mass', 0)) if latest_report.get('muscle_mass') and earliest_report.get('muscle_mass') else 0,
                'reports_count': len(fitness_reports.data)
            }
        
        # 训练总结
        training_summary = {
            'sessions_per_week': round(total_sessions / 4.3, 1) if total_sessions > 0 else 0,
            'average_duration': round(total_duration / total_sessions, 1) if total_sessions > 0 else 0,
            'calories_per_session': round(total_calories / total_sessions, 1) if total_sessions > 0 else 0,
            'intensity_distribution': calculate_intensity_distribution(training_records.data)
        }
        
        return AnalysisReport(
            student_id=student_id,
            student_name=student_name,
            period_start=start_date,
            period_end=end_date,
            total_sessions=total_sessions,
            total_duration=total_duration,
            average_intensity=round(average_intensity, 1),
            total_calories=total_calories,
            fitness_trend=fitness_trend,
            training_summary=training_summary
        )
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成分析报告失败: {str(e)}"
        )

def calculate_intensity_distribution(training_records: List[Dict]) -> Dict[str, int]:
    """计算训练强度分布"""
    distribution = {'low': 0, 'medium': 0, 'high': 0}
    
    for record in training_records:
        intensity = record.get('intensity_level', 0)
        if 1 <= intensity <= 3:
            distribution['low'] += 1
        elif 4 <= intensity <= 6:
            distribution['medium'] += 1
        elif 7 <= intensity <= 10:
            distribution['high'] += 1
    
    return distribution
