"""
用户管理API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.models.user import User, UserUpdate, CoachProfile, StudentProfile
from app.core.auth import get_current_user

router = APIRouter()

@router.get("/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    获取当前用户信息
    """
    return current_user

@router.put("/me", response_model=User)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user)
):
    """
    更新当前用户信息
    """
    try:
        db = get_db()
        
        # 准备更新数据
        update_data = {}
        if user_update.nickname is not None:
            update_data['nickname'] = user_update.nickname
        if user_update.avatar_url is not None:
            update_data['avatar_url'] = user_update.avatar_url
        if user_update.phone is not None:
            update_data['phone'] = user_update.phone
        
        if update_data:
            update_data['updated_at'] = datetime.utcnow().isoformat()
            
            result = db.table('users').update(update_data).eq('id', current_user.id).execute()
            
            if result.data:
                return User(**result.data[0])
        
        return current_user
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户信息失败: {str(e)}"
        )

@router.get("/coaches", response_model=List[User])
async def get_coaches(
    page: int = 1,
    size: int = 10,
    current_user: User = Depends(get_current_user)
):
    """
    获取教练列表
    """
    try:
        db = get_db()
        
        offset = (page - 1) * size
        
        result = db.table('users').select('*').eq('role', 'coach').eq('is_active', True)\
                  .range(offset, offset + size - 1).execute()
        
        coaches = [User(**coach) for coach in result.data]
        return coaches
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取教练列表失败: {str(e)}"
        )

@router.get("/students", response_model=List[User])
async def get_students(
    page: int = 1,
    size: int = 10,
    current_user: User = Depends(get_current_user)
):
    """
    获取学员列表（仅教练可访问）
    """
    if current_user.role != 'coach':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有教练可以查看学员列表"
        )
    
    try:
        db = get_db()
        
        offset = (page - 1) * size
        
        result = db.table('users').select('*').eq('role', 'student').eq('is_active', True)\
                  .range(offset, offset + size - 1).execute()
        
        students = [User(**student) for student in result.data]
        return students
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学员列表失败: {str(e)}"
        )

@router.get("/profile/coach", response_model=CoachProfile)
async def get_coach_profile(current_user: User = Depends(get_current_user)):
    """
    获取教练档案
    """
    if current_user.role != 'coach':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有教练可以访问教练档案"
        )
    
    try:
        db = get_db()
        
        result = db.table('coach_profiles').select('*').eq('user_id', current_user.id).execute()
        
        if not result.data:
            # 如果没有档案，创建一个空档案
            new_profile = {
                'user_id': current_user.id,
                'created_at': datetime.utcnow().isoformat()
            }
            created = db.table('coach_profiles').insert(new_profile).execute()
            return CoachProfile(**created.data[0])
        
        return CoachProfile(**result.data[0])
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取教练档案失败: {str(e)}"
        )

@router.put("/profile/coach", response_model=CoachProfile)
async def update_coach_profile(
    profile_update: CoachProfile,
    current_user: User = Depends(get_current_user)
):
    """
    更新教练档案
    """
    if current_user.role != 'coach':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有教练可以更新教练档案"
        )
    
    try:
        db = get_db()
        
        update_data = profile_update.dict(exclude={'user_id'}, exclude_unset=True)
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        result = db.table('coach_profiles').update(update_data).eq('user_id', current_user.id).execute()
        
        if result.data:
            return CoachProfile(**result.data[0])
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="教练档案不存在"
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新教练档案失败: {str(e)}"
        )

@router.get("/profile/student", response_model=StudentProfile)
async def get_student_profile(current_user: User = Depends(get_current_user)):
    """
    获取学员档案
    """
    if current_user.role != 'student':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有学员可以访问学员档案"
        )
    
    try:
        db = get_db()
        
        result = db.table('student_profiles').select('*').eq('user_id', current_user.id).execute()
        
        if not result.data:
            # 如果没有档案，创建一个空档案
            new_profile = {
                'user_id': current_user.id,
                'created_at': datetime.utcnow().isoformat()
            }
            created = db.table('student_profiles').insert(new_profile).execute()
            return StudentProfile(**created.data[0])
        
        return StudentProfile(**result.data[0])
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学员档案失败: {str(e)}"
        )

@router.put("/profile/student", response_model=StudentProfile)
async def update_student_profile(
    profile_update: StudentProfile,
    current_user: User = Depends(get_current_user)
):
    """
    更新学员档案
    """
    if current_user.role != 'student':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有学员可以更新学员档案"
        )
    
    try:
        db = get_db()
        
        update_data = profile_update.dict(exclude={'user_id'}, exclude_unset=True)
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        result = db.table('student_profiles').update(update_data).eq('user_id', current_user.id).execute()
        
        if result.data:
            return StudentProfile(**result.data[0])
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学员档案不存在"
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新学员档案失败: {str(e)}"
        )
