"""
课程排期API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import datetime, date

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_coach
from app.models.user import User
from app.models.schedule import (
    Schedule, ScheduleCreate, ScheduleUpdate, ScheduleQuery,
    ScheduleConfirmRequest, ScheduleRescheduleRequest, ScheduleStatus
)

router = APIRouter()

@router.post("/", response_model=Schedule)
async def create_schedule(
    schedule_data: ScheduleCreate,
    current_user: User = Depends(get_current_coach)
):
    """
    创建课程排期（仅教练可创建）
    """
    try:
        db = get_db()
        
        # 验证学员是否存在
        student_result = db.table('users').select('id, nickname').eq('id', schedule_data.student_id)\
                          .eq('role', 'student').eq('is_active', True).execute()
        
        if not student_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="学员不存在"
            )
        
        # 检查时间冲突
        conflict_check = db.table('schedules').select('id')\
                          .eq('coach_id', current_user.id)\
                          .in_('status', ['pending', 'confirmed'])\
                          .gte('start_time', schedule_data.start_time.isoformat())\
                          .lte('start_time', schedule_data.end_time.isoformat()).execute()
        
        if conflict_check.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该时间段已有其他课程安排"
            )
        
        # 创建排期
        new_schedule = {
            **schedule_data.dict(),
            'coach_id': current_user.id,
            'start_time': schedule_data.start_time.isoformat(),
            'end_time': schedule_data.end_time.isoformat(),
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }
        
        result = db.table('schedules').insert(new_schedule).execute()
        
        if result.data:
            # 获取完整信息（包含关联的用户名）
            schedule_with_names = await get_schedule_with_names(result.data[0]['id'])
            return schedule_with_names
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建课程排期失败"
        )
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建课程排期失败: {str(e)}"
        )

@router.get("/", response_model=List[Schedule])
async def get_schedules(
    coach_id: Optional[int] = Query(None, description="教练ID"),
    student_id: Optional[int] = Query(None, description="学员ID"),
    status: Optional[ScheduleStatus] = Query(None, description="课程状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    current_user: User = Depends(get_current_user)
):
    """
    获取课程排期列表
    """
    try:
        db = get_db()
        
        # 构建查询
        query = db.table('schedules').select('''
            *,
            coach:coach_id(nickname),
            student:student_id(nickname)
        ''')
        
        # 权限控制：用户只能查看与自己相关的排期
        if current_user.role == 'coach':
            if coach_id and coach_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能查看自己的课程排期"
                )
            query = query.eq('coach_id', current_user.id)
        elif current_user.role == 'student':
            if student_id and student_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只能查看自己的课程排期"
                )
            query = query.eq('student_id', current_user.id)
        
        # 应用过滤条件
        if coach_id:
            query = query.eq('coach_id', coach_id)
        if student_id:
            query = query.eq('student_id', student_id)
        if status:
            query = query.eq('status', status.value)
        if start_date:
            query = query.gte('start_time', start_date.isoformat())
        if end_date:
            query = query.lte('end_time', end_date.isoformat())
        
        # 分页
        offset = (page - 1) * size
        query = query.range(offset, offset + size - 1)
        
        # 排序
        query = query.order('start_time', desc=False)
        
        result = query.execute()
        
        schedules = []
        for schedule_data in result.data:
            schedule = Schedule(**schedule_data)
            # 添加关联用户名
            if schedule_data.get('coach'):
                schedule.coach_name = schedule_data['coach']['nickname']
            if schedule_data.get('student'):
                schedule.student_name = schedule_data['student']['nickname']
            schedules.append(schedule)
        
        return schedules
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取课程排期失败: {str(e)}"
        )

@router.get("/{schedule_id}", response_model=Schedule)
async def get_schedule(
    schedule_id: int,
    current_user: User = Depends(get_current_user)
):
    """
    获取单个课程排期详情
    """
    try:
        schedule = await get_schedule_with_names(schedule_id)
        
        # 权限检查
        if current_user.role == 'coach' and schedule.coach_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此课程排期"
            )
        elif current_user.role == 'student' and schedule.student_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此课程排期"
            )
        
        return schedule
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取课程排期失败: {str(e)}"
        )

@router.put("/{schedule_id}", response_model=Schedule)
async def update_schedule(
    schedule_id: int,
    schedule_update: ScheduleUpdate,
    current_user: User = Depends(get_current_coach)
):
    """
    更新课程排期（仅教练可更新）
    """
    try:
        db = get_db()
        
        # 检查排期是否存在且属于当前教练
        existing = db.table('schedules').select('*').eq('id', schedule_id)\
                    .eq('coach_id', current_user.id).execute()
        
        if not existing.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="课程排期不存在或无权修改"
            )
        
        # 准备更新数据
        update_data = schedule_update.dict(exclude_unset=True)
        if 'start_time' in update_data:
            update_data['start_time'] = update_data['start_time'].isoformat()
        if 'end_time' in update_data:
            update_data['end_time'] = update_data['end_time'].isoformat()
        
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        result = db.table('schedules').update(update_data).eq('id', schedule_id).execute()
        
        if result.data:
            return await get_schedule_with_names(schedule_id)
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新课程排期失败"
        )
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新课程排期失败: {str(e)}"
        )

@router.post("/{schedule_id}/confirm")
async def confirm_schedule(
    schedule_id: int,
    confirm_data: ScheduleConfirmRequest,
    current_user: User = Depends(get_current_user)
):
    """
    确认或取消课程排期
    """
    try:
        db = get_db()
        
        # 获取排期信息
        schedule_result = db.table('schedules').select('*').eq('id', schedule_id).execute()
        
        if not schedule_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="课程排期不存在"
            )
        
        schedule = schedule_result.data[0]
        
        # 权限检查：教练和学员都可以确认/取消
        if (current_user.role == 'coach' and schedule['coach_id'] != current_user.id) or \
           (current_user.role == 'student' and schedule['student_id'] != current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权操作此课程排期"
            )
        
        # 更新状态
        new_status = 'confirmed' if confirm_data.action == 'confirm' else 'cancelled'
        
        update_result = db.table('schedules').update({
            'status': new_status,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('id', schedule_id).execute()
        
        if update_result.data:
            # TODO: 发送通知给相关用户
            return {"message": f"课程已{('确认' if confirm_data.action == 'confirm' else '取消')}"}
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="操作失败"
        )
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"操作失败: {str(e)}"
        )

async def get_schedule_with_names(schedule_id: int) -> Schedule:
    """
    获取包含用户名的排期信息
    """
    db = get_db()
    
    result = db.table('schedules').select('''
        *,
        coach:coach_id(nickname),
        student:student_id(nickname)
    ''').eq('id', schedule_id).execute()
    
    if not result.data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="课程排期不存在"
        )
    
    schedule_data = result.data[0]
    schedule = Schedule(**schedule_data)
    
    # 添加关联用户名
    if schedule_data.get('coach'):
        schedule.coach_name = schedule_data['coach']['nickname']
    if schedule_data.get('student'):
        schedule.student_name = schedule_data['student']['nickname']
    
    return schedule
