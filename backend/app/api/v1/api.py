"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, schedules, analysis

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(schedules.router, prefix="/schedules", tags=["课程排期"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["数据分析"])
