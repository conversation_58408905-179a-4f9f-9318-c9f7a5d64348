#!/bin/bash

# CoachTeacher 项目演示脚本
# 用于快速演示项目功能

set -e

echo "🎯 CoachTeacher 项目演示"
echo "=========================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色文本
print_color() {
    printf "${2}${1}${NC}\n"
}

print_color "📋 项目概述" $BLUE
echo "CoachTeacher 是一个专业的教练-学员管理微信小程序"
echo "主要功能："
echo "  • 🔐 微信登录与角色管理"
echo "  • 📅 智能课程排期管理"
echo "  • 📊 训练数据分析"
echo "  • 💬 实时沟通功能"
echo ""

print_color "🏗️ 技术架构" $BLUE
echo "前端：原生微信小程序"
echo "后端：Python + FastAPI"
echo "数据库：Supabase (PostgreSQL)"
echo "认证：JWT + 微信登录"
echo ""

print_color "📁 项目结构" $BLUE
tree -L 2 -I '__pycache__|*.pyc|node_modules' . 2>/dev/null || find . -type d -maxdepth 2 | head -20
echo ""

print_color "🧪 后端功能演示" $YELLOW
echo "正在检查后端依赖..."

# 检查Python
if command -v python3 &> /dev/null; then
    print_color "✅ Python3 已安装: $(python3 --version)" $GREEN
else
    print_color "❌ Python3 未安装" $RED
    exit 1
fi

# 检查后端文件
if [ -f "backend/main.py" ]; then
    print_color "✅ 后端文件存在" $GREEN
else
    print_color "❌ 后端文件不存在" $RED
    exit 1
fi

echo ""
print_color "🔧 安装后端依赖..." $YELLOW
cd backend

# 检查并安装依赖
if pip3 show fastapi uvicorn &> /dev/null; then
    print_color "✅ 核心依赖已安装" $GREEN
else
    print_color "📦 安装核心依赖..." $YELLOW
    pip3 install fastapi uvicorn python-dotenv pydantic-settings --quiet
    print_color "✅ 依赖安装完成" $GREEN
fi

echo ""
print_color "🚀 启动后端服务..." $YELLOW

# 检查端口
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    print_color "⚠️  端口8000已被占用，尝试停止现有服务..." $YELLOW
    pkill -f "uvicorn main:app" 2>/dev/null || true
    sleep 2
fi

# 启动后端服务
python3 -c "from main import app; print('✅ FastAPI应用加载成功')" 2>/dev/null || {
    print_color "❌ 后端应用加载失败，可能缺少依赖" $RED
    echo "请运行: pip3 install -r requirements.txt"
    exit 1
}

# 后台启动服务
nohup python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 > /dev/null 2>&1 &
BACKEND_PID=$!

# 等待服务启动
sleep 3

echo ""
print_color "🔍 测试API端点..." $YELLOW

# 测试健康检查
if curl -s http://localhost:8000/health | grep -q "healthy"; then
    print_color "✅ 健康检查通过" $GREEN
else
    print_color "❌ 健康检查失败" $RED
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# 测试根端点
if curl -s http://localhost:8000/ | grep -q "CoachTeacher"; then
    print_color "✅ 根端点响应正常" $GREEN
else
    print_color "❌ 根端点响应异常" $RED
fi

echo ""
print_color "📱 前端功能演示" $YELLOW

cd ../miniprogram

# 检查小程序文件
if [ -f "app.js" ] && [ -f "app.json" ]; then
    print_color "✅ 小程序文件完整" $GREEN
else
    print_color "❌ 小程序文件不完整" $RED
fi

# 检查页面文件
PAGE_COUNT=$(find pages -name "*.wxml" | wc -l)
print_color "✅ 发现 $PAGE_COUNT 个页面文件" $GREEN

# 检查组件
if [ -d "components" ]; then
    print_color "✅ 组件目录存在" $GREEN
fi

echo ""
print_color "📊 项目统计" $BLUE

cd ..

# 统计代码行数
echo "代码统计："
echo "  Python文件: $(find backend -name "*.py" | wc -l) 个"
echo "  小程序页面: $(find miniprogram/pages -name "*.wxml" | wc -l) 个"
echo "  样式文件: $(find miniprogram -name "*.wxss" | wc -l) 个"
echo "  JavaScript文件: $(find miniprogram -name "*.js" | wc -l) 个"

# 统计总行数
PYTHON_LINES=$(find backend -name "*.py" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
JS_LINES=$(find miniprogram -name "*.js" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
echo "  Python代码行数: $PYTHON_LINES"
echo "  JavaScript代码行数: $JS_LINES"

echo ""
print_color "🌐 访问地址" $BLUE
echo "  API服务: http://localhost:8000"
echo "  API文档: http://localhost:8000/docs"
echo "  健康检查: http://localhost:8000/health"

echo ""
print_color "📚 项目文档" $BLUE
echo "  快速开始: ./QUICK_START.md"
echo "  开发指南: ./docs/Development_Guide.md"
echo "  UI设计: ./docs/UI_Design_Prototype.md"
echo "  项目总结: ./PROJECT_SUMMARY.md"

echo ""
print_color "🎉 演示完成！" $GREEN
echo ""
echo "后续步骤："
echo "1. 📖 阅读 QUICK_START.md 了解详细配置"
echo "2. 🔧 配置 Supabase 数据库"
echo "3. 📱 在微信开发者工具中打开小程序"
echo "4. 🚀 开始开发和定制功能"

echo ""
print_color "⚠️  注意：演示服务将在后台运行" $YELLOW
echo "停止服务: kill $BACKEND_PID"
echo "或运行: pkill -f 'uvicorn main:app'"

echo ""
print_color "🤝 需要帮助？" $BLUE
echo "查看项目文档或提交Issue获取支持"
