# CoachTeacher UI设计原型

## 设计理念

### 色彩方案
- **主色调**: #1890ff (蓝色) - 专业、可信赖
- **辅助色**: #52c41a (绿色) - 成功、健康
- **警告色**: #faad14 (橙色) - 提醒、注意
- **错误色**: #f5222d (红色) - 错误、取消
- **背景色**: #f5f5f5 (浅灰) - 简洁、舒适

### 设计原则
1. **简洁明了**: 界面简洁，信息层次清晰
2. **一致性**: 统一的设计语言和交互模式
3. **易用性**: 符合用户习惯，操作便捷
4. **专业性**: 体现健身教练行业的专业特色

## 页面结构设计

### 1. 登录页面 (Login)
```
┌─────────────────────────────────┐
│           Logo & 标题            │
│         CoachTeacher           │
│      专业的教练学员管理平台        │
├─────────────────────────────────┤
│         选择身份                 │
│   [我是教练]    [我是学员]        │
├─────────────────────────────────┤
│        [微信一键登录]             │
│                                │
│    □ 我已阅读并同意用户协议       │
├─────────────────────────────────┤
│        功能特色展示              │
│  📅智能排课  💬实时沟通  📊数据分析 │
└─────────────────────────────────┘
```

### 2. 首页 (Home)
```
┌─────────────────────────────────┐
│        用户信息卡片              │
│  👤 用户名    📊 总课程: 12      │
│     教练/学员     本周: 3        │
├─────────────────────────────────┤
│        快捷操作                 │
│ [创建课程] [学员管理] [训练记录]   │
│ [数据分析] [我的课程] [找教练]    │
├─────────────────────────────────┤
│        今日课程                 │
│  09:00  │ 力量训练课程           │
│  60分钟  │ 学员: 张三            │
│         │ 📍 健身房A区          │
├─────────────────────────────────┤
│        最新通知                 │
│  🔔 课程提醒: 明天上午9点...      │
│  📝 学员反馈: 训练强度适中...      │
└─────────────────────────────────┘
```

### 3. 课程排期页面 (Schedule)
```
┌─────────────────────────────────┐
│    📅 2024年1月    [+新建]      │
├─────────────────────────────────┤
│  日  一  二  三  四  五  六      │
│      1   2   3   4   5   6      │
│  7   8   9  10  11  12  13      │
│ 14  15  16  17  18  19  20      │
│ 21  22  23  24  25  26  27      │
│ 28  29  30  31                  │
├─────────────────────────────────┤
│        今日课程列表              │
│ ┌─────────────────────────────┐ │
│ │ 09:00-10:00  力量训练       │ │
│ │ 学员: 张三    状态: 已确认   │ │
│ └─────────────────────────────┘ │
│ ┌─────────────────────────────┐ │
│ │ 14:00-15:30  有氧训练       │ │
│ │ 学员: 李四    状态: 待确认   │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

### 4. 数据分析页面 (Analysis)
```
┌─────────────────────────────────┐
│        本月数据概览              │
│  训练次数: 12    训练时长: 720分钟│
│  消耗卡路里: 3600  平均强度: 7.5 │
├─────────────────────────────────┤
│        体测数据趋势              │
│  📊 体重变化图表                │
│  📊 体脂率变化图表              │
│  📊 肌肉量变化图表              │
├─────────────────────────────────┤
│        训练强度分析              │
│  低强度: 20%  中强度: 50%       │
│  高强度: 30%                   │
├─────────────────────────────────┤
│        [生成详细报告]            │
└─────────────────────────────────┘
```

### 5. 个人中心页面 (Profile)
```
┌─────────────────────────────────┐
│        个人信息                 │
│  👤 头像    用户名              │
│            手机号              │
│            [编辑资料]           │
├─────────────────────────────────┤
│        功能设置                 │
│  🔔 消息通知                   │
│  🔒 隐私设置                   │
│  ❓ 帮助中心                   │
│  📞 联系客服                   │
├─────────────────────────────────┤
│        其他                    │
│  📋 用户协议                   │
│  🔐 隐私政策                   │
│  🚪 退出登录                   │
└─────────────────────────────────┘
```

## 组件设计

### 1. 课程卡片组件
```
┌─────────────────────────────────┐
│ 09:00-10:00        [状态标签]    │
│ 力量训练课程                    │
│ 👤 学员: 张三                   │
│ 📍 健身房A区                    │
│ 💰 ¥200                       │
└─────────────────────────────────┘
```

### 2. 状态标签组件
- 待确认: 橙色背景 `#fff7e6`
- 已确认: 绿色背景 `#f6ffed`
- 已取消: 红色背景 `#fff2f0`
- 已完成: 灰色背景 `#f0f0f0`

### 3. 数据卡片组件
```
┌─────────────────┐
│       12        │
│    训练次数      │
└─────────────────┘
```

### 4. 操作按钮组件
- 主要按钮: 蓝色背景，白色文字
- 次要按钮: 白色背景，蓝色边框
- 危险按钮: 红色背景，白色文字

## 交互设计

### 1. 页面跳转
- Tab切换: 底部导航栏
- 页面跳转: 右滑进入，左滑返回
- 弹窗: 从底部滑入

### 2. 操作反馈
- 点击反馈: 轻微缩放动画
- 加载状态: 转圈加载动画
- 成功提示: 绿色Toast
- 错误提示: 红色Toast

### 3. 手势操作
- 下拉刷新: 页面数据刷新
- 上拉加载: 分页数据加载
- 左滑删除: 列表项删除

## 响应式设计

### 屏幕适配
- 基准尺寸: iPhone 6/7/8 (375px)
- 单位使用: rpx (响应式像素)
- 字体大小: 24rpx - 48rpx
- 间距规范: 8rpx的倍数

### 组件尺寸
- 按钮高度: 88rpx
- 输入框高度: 80rpx
- 列表项高度: 120rpx
- 卡片圆角: 12rpx
- 头像尺寸: 80rpx

## 图标设计

### 图标风格
- 线性图标为主
- 统一的线条粗细
- 简洁明了的设计
- 24px基准尺寸

### 主要图标
- 首页: home
- 排期: calendar
- 分析: chart
- 我的: user
- 添加: plus
- 设置: settings
- 通知: bell
- 位置: location

## 动画效果

### 页面转场
- 进入: 从右侧滑入 (300ms)
- 退出: 向右侧滑出 (300ms)
- 弹窗: 从底部滑入 (250ms)

### 微交互
- 按钮点击: scale(0.95) (150ms)
- 列表加载: 渐入动画 (200ms)
- 状态变化: 颜色渐变 (300ms)

## 可访问性

### 文字对比度
- 主要文字: #333333 (AAA级)
- 次要文字: #666666 (AA级)
- 辅助文字: #999999 (AA级)

### 触摸目标
- 最小点击区域: 88rpx × 88rpx
- 按钮间距: 至少16rpx
- 文字行高: 1.4-1.6倍

### 信息层次
- 标题: 32rpx-48rpx, 粗体
- 正文: 28rpx-32rpx, 常规
- 辅助: 24rpx-28rpx, 常规
