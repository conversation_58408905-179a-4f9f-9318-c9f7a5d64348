# CoachTeacher Docker容器化指南

## 🐳 概述

CoachTeacher项目已完全容器化，支持一键部署和管理。本指南将帮助您快速上手Docker部署。

## 📋 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <your-repo-url>
cd CoachTeacher

# 配置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件
```

### 2. 一键启动
```bash
# 构建并启动所有服务
./docker-manage.sh build
./docker-manage.sh start -d

# 或使用Docker Compose
docker-compose up -d
```

### 3. 验证部署
```bash
# 检查服务状态
./docker-manage.sh status

# 查看日志
./docker-manage.sh logs

# 测试API
curl http://localhost/health
```

## 🏗️ 架构组件

### 核心服务
- **backend**: FastAPI后端服务 (端口: 8000)
- **nginx**: 反向代理和负载均衡 (端口: 80/443)
- **redis**: 缓存服务 (端口: 6379)

### 监控服务 (可选)
- **prometheus**: 监控数据收集 (端口: 9090)
- **grafana**: 监控仪表板 (端口: 3000)

## 🔧 管理命令

### 基本操作
```bash
# 构建镜像
./docker-manage.sh build

# 启动服务
./docker-manage.sh start -d

# 停止服务
./docker-manage.sh stop

# 重启服务
./docker-manage.sh restart

# 查看状态
./docker-manage.sh status
```

### 日志管理
```bash
# 查看所有日志
./docker-manage.sh logs

# 查看特定服务日志
./docker-manage.sh logs backend
./docker-manage.sh logs nginx
./docker-manage.sh logs redis
```

### 容器管理
```bash
# 进入后端容器
./docker-manage.sh shell backend

# 进入Nginx容器
./docker-manage.sh shell nginx

# 进入Redis容器
./docker-manage.sh shell redis
```

### 数据管理
```bash
# 备份数据
./docker-manage.sh backup

# 恢复数据
./docker-manage.sh restore backups/20231201_120000

# 清理资源
./docker-manage.sh clean
```

### 监控服务
```bash
# 启动监控
./docker-manage.sh monitor

# 访问监控面板
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000 (admin/admin123)
```

## 📊 服务访问

### 主要端点
- **API服务**: http://localhost
- **API文档**: http://localhost/docs
- **健康检查**: http://localhost/health
- **监控指标**: http://localhost/metrics

### 监控面板
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000

## 🔒 安全配置

### 生产环境建议
1. **SSL证书配置**
```bash
# 将SSL证书放入nginx/ssl目录
cp your-cert.pem nginx/ssl/
cp your-key.pem nginx/ssl/

# 更新nginx配置启用HTTPS
```

2. **环境变量安全**
```bash
# 使用强密码
SECRET_KEY=your-very-strong-secret-key
REDIS_PASSWORD=your-redis-password

# 限制数据库访问
SUPABASE_SERVICE_KEY=your-service-key
```

3. **网络安全**
```bash
# 修改默认端口
# 配置防火墙规则
# 启用访问日志监控
```

## 🔧 自定义配置

### 修改Nginx配置
```bash
# 编辑配置文件
vim nginx/conf.d/default.conf

# 重启Nginx
docker-compose restart nginx
```

### 调整资源限制
```yaml
# 在docker-compose.yml中添加
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

### 添加环境变量
```bash
# 编辑.env文件
echo "NEW_VARIABLE=value" >> backend/.env

# 重启服务
./docker-manage.sh restart
```

## 📈 性能优化

### 缓存配置
```bash
# Redis内存限制
echo "maxmemory 256mb" >> redis.conf
echo "maxmemory-policy allkeys-lru" >> redis.conf
```

### 数据库连接池
```python
# 在backend配置中调整
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
```

### Nginx优化
```nginx
# 在nginx.conf中添加
worker_processes auto;
worker_connections 2048;
keepalive_timeout 30;
```

## 🐛 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看详细日志
docker-compose logs backend

# 检查端口占用
netstat -tulpn | grep :8000

# 重新构建镜像
./docker-manage.sh build
```

#### 2. 数据库连接失败
```bash
# 检查环境变量
cat backend/.env | grep SUPABASE

# 测试网络连接
docker-compose exec backend ping supabase.com
```

#### 3. Redis连接失败
```bash
# 检查Redis状态
docker-compose exec redis redis-cli ping

# 重启Redis
docker-compose restart redis
```

#### 4. Nginx配置错误
```bash
# 测试配置
docker-compose exec nginx nginx -t

# 重新加载配置
docker-compose exec nginx nginx -s reload
```

### 日志分析
```bash
# 查看错误日志
tail -f logs/nginx/error.log

# 查看访问日志
tail -f logs/nginx/access.log

# 查看应用日志
./docker-manage.sh logs backend | grep ERROR
```

## 📦 部署到生产环境

### 1. 服务器准备
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
pip install docker-compose
```

### 2. 项目部署
```bash
# 上传项目文件
scp -r CoachTeacher user@server:/opt/

# 登录服务器
ssh user@server
cd /opt/CoachTeacher

# 配置环境
cp backend/.env.example backend/.env
vim backend/.env

# 启动服务
./docker-manage.sh build
./docker-manage.sh start -d
```

### 3. 域名配置
```bash
# 配置DNS解析
# 更新nginx配置中的server_name
# 申请SSL证书
```

### 4. 监控设置
```bash
# 启动监控服务
./docker-manage.sh monitor

# 配置告警规则
# 设置日志轮转
```

## 🔄 更新和维护

### 应用更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
./docker-manage.sh build

# 滚动更新
docker-compose up -d --no-deps backend
```

### 数据备份
```bash
# 定期备份
./docker-manage.sh backup

# 设置定时任务
echo "0 2 * * * /opt/CoachTeacher/docker-manage.sh backup" | crontab -
```

### 日志清理
```bash
# 清理Docker日志
docker system prune -f

# 清理应用日志
find logs -name "*.log" -mtime +7 -delete
```

## 📞 技术支持

如遇问题，请：
1. 查看本文档的故障排除部分
2. 检查项目日志文件
3. 提交Issue并附上详细错误信息

---

**🎉 恭喜！您已成功掌握CoachTeacher的Docker部署！**
