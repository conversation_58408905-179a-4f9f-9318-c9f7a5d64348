# CoachTeacher 开发指南

## 环境准备

### 系统要求
- Python 3.8+
- Node.js 14+
- 微信开发者工具
- Git

### 开发工具
- **后端开发**: VS Code + Python扩展
- **前端开发**: 微信开发者工具
- **数据库管理**: Supabase Dashboard
- **API测试**: Postman 或 Thunder Client

## 项目设置

### 1. 克隆项目
```bash
git clone <repository-url>
cd CoachTeacher
```

### 2. 后端设置
```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 复制环境变量文件
cp .env.example .env
```

### 3. 配置环境变量
编辑 `backend/.env` 文件：
```env
# Supabase配置
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# JWT配置
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

### 4. 数据库初始化
在Supabase Dashboard中执行 `backend/database_schema.sql` 文件中的SQL语句。

### 5. 启动后端服务
```bash
cd backend
uvicorn main:app --reload
```
服务将在 http://localhost:8000 启动

### 6. 前端设置
1. 使用微信开发者工具打开 `miniprogram` 目录
2. 配置AppID（在project.config.json中）
3. 修改 `miniprogram/app.js` 中的 `baseUrl` 为后端服务地址

## 开发流程

### 1. 功能开发流程
1. **需求分析**: 明确功能需求和用户故事
2. **API设计**: 设计RESTful API接口
3. **数据模型**: 定义数据库表结构和Pydantic模型
4. **后端开发**: 实现API端点和业务逻辑
5. **前端开发**: 实现小程序页面和交互
6. **测试验证**: 单元测试和集成测试
7. **代码审查**: Code Review和优化

### 2. Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 开发完成后提交
git add .
git commit -m "feat: add new feature"

# 推送到远程分支
git push origin feature/new-feature

# 创建Pull Request
# 代码审查通过后合并到main分支
```

### 3. 代码规范

#### Python代码规范
- 遵循PEP 8规范
- 使用类型注解
- 函数和类添加文档字符串
- 变量和函数使用snake_case命名

```python
from typing import List, Optional
from pydantic import BaseModel

class UserCreate(BaseModel):
    """创建用户的数据模型"""
    nickname: str
    phone: Optional[str] = None
    
async def create_user(user_data: UserCreate) -> User:
    """
    创建新用户
    
    Args:
        user_data: 用户创建数据
        
    Returns:
        创建的用户对象
    """
    # 实现逻辑
    pass
```

#### JavaScript代码规范
- 使用ES6+语法
- 函数和变量使用camelCase命名
- 常量使用UPPER_CASE命名
- 添加必要的注释

```javascript
// 获取用户信息
async function getUserInfo() {
  try {
    const response = await app.request({
      url: '/users/me',
      method: 'GET'
    })
    return response
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}
```

## API文档

### 认证接口
- `POST /api/v1/auth/login` - 微信登录
- `POST /api/v1/auth/refresh` - 刷新令牌

### 用户管理
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/me` - 更新用户信息
- `GET /api/v1/users/coaches` - 获取教练列表
- `GET /api/v1/users/students` - 获取学员列表

### 课程排期
- `GET /api/v1/schedules` - 获取课程列表
- `POST /api/v1/schedules` - 创建课程
- `GET /api/v1/schedules/{id}` - 获取课程详情
- `PUT /api/v1/schedules/{id}` - 更新课程
- `POST /api/v1/schedules/{id}/confirm` - 确认/取消课程

### 数据分析
- `GET /api/v1/analysis/fitness-reports` - 获取体测报告
- `POST /api/v1/analysis/fitness-reports` - 创建体测报告
- `GET /api/v1/analysis/training-records` - 获取训练记录
- `POST /api/v1/analysis/training-records` - 创建训练记录
- `GET /api/v1/analysis/report/{student_id}` - 获取分析报告

## 测试

### 后端测试
```bash
cd backend
pytest tests/ -v
```

### API测试
使用Postman或Thunder Client测试API端点：
1. 导入API文档
2. 设置环境变量
3. 测试各个端点的功能

### 前端测试
1. 在微信开发者工具中预览
2. 真机调试测试
3. 性能分析

## 部署

### 后端部署
1. **服务器准备**
   - 云服务器（推荐腾讯云/阿里云）
   - Python 3.8+环境
   - Nginx反向代理

2. **部署步骤**
```bash
# 克隆代码
git clone <repository-url>
cd CoachTeacher/backend

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件

# 启动服务
uvicorn main:app --host 0.0.0.0 --port 8000
```

3. **Nginx配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 前端部署
1. **小程序发布**
   - 在微信开发者工具中点击"上传"
   - 在微信公众平台提交审核
   - 审核通过后发布

2. **配置域名**
   - 在微信公众平台配置服务器域名
   - 确保HTTPS证书有效

## 监控和维护

### 日志监控
- 使用Python logging模块记录日志
- 配置日志轮转和存储
- 监控错误日志和性能指标

### 性能优化
- 数据库查询优化
- API响应时间监控
- 小程序性能分析

### 安全考虑
- API接口鉴权
- 数据传输加密
- 用户隐私保护
- SQL注入防护

## 常见问题

### 1. 微信登录失败
- 检查AppID和AppSecret配置
- 确认小程序域名配置
- 验证网络连接

### 2. 数据库连接失败
- 检查Supabase配置
- 验证网络连接
- 确认数据库权限

### 3. API请求失败
- 检查后端服务状态
- 验证API地址配置
- 查看网络请求日志

## 开发工具推荐

### VS Code扩展
- Python
- Pylance
- Thunder Client
- GitLens
- Prettier

### 调试工具
- 微信开发者工具调试器
- Chrome DevTools
- Postman
- Supabase Dashboard

## 参考资源

- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Supabase官方文档](https://supabase.com/docs)
- [Pydantic官方文档](https://pydantic-docs.helpmanual.io/)
