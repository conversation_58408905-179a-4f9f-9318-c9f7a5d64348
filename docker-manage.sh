#!/bin/bash

# CoachTeacher Docker管理脚本
# 用于管理Docker容器化部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色文本
print_color() {
    printf "${2}${1}${NC}\n"
}

# 显示帮助信息
show_help() {
    echo "CoachTeacher Docker管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  build       构建Docker镜像"
    echo "  start       启动所有服务"
    echo "  stop        停止所有服务"
    echo "  restart     重启所有服务"
    echo "  logs        查看服务日志"
    echo "  status      查看服务状态"
    echo "  clean       清理未使用的资源"
    echo "  backup      备份数据"
    echo "  restore     恢复数据"
    echo "  monitor     启动监控服务"
    echo "  shell       进入容器shell"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -v, --verbose  详细输出"
    echo "  -d, --detach   后台运行"
    echo ""
    echo "示例:"
    echo "  $0 build              # 构建镜像"
    echo "  $0 start -d           # 后台启动服务"
    echo "  $0 logs backend       # 查看后端日志"
    echo "  $0 shell backend      # 进入后端容器"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_color "❌ Docker未安装，请先安装Docker" $RED
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_color "❌ Docker Compose未安装，请先安装Docker Compose" $RED
        exit 1
    fi
}

# 检查环境文件
check_env() {
    if [ ! -f "backend/.env" ]; then
        print_color "⚠️  环境文件不存在，正在创建..." $YELLOW
        cp backend/.env.example backend/.env
        print_color "📝 请编辑 backend/.env 文件配置必要的环境变量" $YELLOW
        return 1
    fi
    return 0
}

# 构建镜像
build_images() {
    print_color "🔨 构建Docker镜像..." $BLUE
    
    if [ "$VERBOSE" = true ]; then
        docker-compose build --no-cache
    else
        docker-compose build --no-cache > /dev/null 2>&1
    fi
    
    print_color "✅ 镜像构建完成" $GREEN
}

# 启动服务
start_services() {
    print_color "🚀 启动CoachTeacher服务..." $BLUE
    
    local compose_args=""
    if [ "$DETACH" = true ]; then
        compose_args="-d"
    fi
    
    docker-compose up $compose_args
    
    if [ "$DETACH" = true ]; then
        print_color "✅ 服务已在后台启动" $GREEN
        print_color "🌐 API地址: http://localhost" $BLUE
        print_color "📚 API文档: http://localhost/docs" $BLUE
        print_color "📊 健康检查: http://localhost/health" $BLUE
    fi
}

# 停止服务
stop_services() {
    print_color "🛑 停止CoachTeacher服务..." $YELLOW
    docker-compose down
    print_color "✅ 服务已停止" $GREEN
}

# 重启服务
restart_services() {
    print_color "🔄 重启CoachTeacher服务..." $BLUE
    docker-compose restart
    print_color "✅ 服务已重启" $GREEN
}

# 查看日志
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f $service
    fi
}

# 查看状态
show_status() {
    print_color "📊 CoachTeacher服务状态:" $BLUE
    docker-compose ps
    
    echo ""
    print_color "💾 磁盘使用情况:" $BLUE
    docker system df
    
    echo ""
    print_color "🔗 网络信息:" $BLUE
    docker network ls | grep coachteacher
}

# 清理资源
clean_resources() {
    print_color "🧹 清理未使用的Docker资源..." $YELLOW
    
    # 清理停止的容器
    docker container prune -f
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    print_color "✅ 清理完成" $GREEN
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $backup_dir
    
    print_color "💾 备份数据到 $backup_dir..." $BLUE
    
    # 备份Redis数据
    docker-compose exec redis redis-cli BGSAVE
    docker cp $(docker-compose ps -q redis):/data/dump.rdb $backup_dir/redis_dump.rdb
    
    # 备份日志
    cp -r logs $backup_dir/
    
    # 备份配置
    cp -r nginx $backup_dir/
    cp docker-compose.yml $backup_dir/
    cp backend/.env $backup_dir/env_backup
    
    print_color "✅ 备份完成: $backup_dir" $GREEN
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    if [ -z "$backup_dir" ]; then
        print_color "❌ 请指定备份目录" $RED
        exit 1
    fi
    
    if [ ! -d "$backup_dir" ]; then
        print_color "❌ 备份目录不存在: $backup_dir" $RED
        exit 1
    fi
    
    print_color "📥 从 $backup_dir 恢复数据..." $BLUE
    
    # 恢复Redis数据
    if [ -f "$backup_dir/redis_dump.rdb" ]; then
        docker cp $backup_dir/redis_dump.rdb $(docker-compose ps -q redis):/data/dump.rdb
        docker-compose restart redis
    fi
    
    print_color "✅ 数据恢复完成" $GREEN
}

# 启动监控
start_monitoring() {
    print_color "📊 启动监控服务..." $BLUE
    docker-compose --profile monitoring up -d prometheus grafana
    
    print_color "✅ 监控服务已启动" $GREEN
    print_color "📊 Prometheus: http://localhost:9090" $BLUE
    print_color "📈 Grafana: http://localhost:3000 (admin/admin123)" $BLUE
}

# 进入容器shell
enter_shell() {
    local service=$1
    if [ -z "$service" ]; then
        service="backend"
    fi
    
    print_color "🐚 进入 $service 容器..." $BLUE
    docker-compose exec $service /bin/bash
}

# 解析命令行参数
VERBOSE=false
DETACH=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -d|--detach)
            DETACH=true
            shift
            ;;
        build)
            COMMAND="build"
            shift
            ;;
        start)
            COMMAND="start"
            shift
            ;;
        stop)
            COMMAND="stop"
            shift
            ;;
        restart)
            COMMAND="restart"
            shift
            ;;
        logs)
            COMMAND="logs"
            SERVICE=$2
            shift 2
            ;;
        status)
            COMMAND="status"
            shift
            ;;
        clean)
            COMMAND="clean"
            shift
            ;;
        backup)
            COMMAND="backup"
            shift
            ;;
        restore)
            COMMAND="restore"
            BACKUP_DIR=$2
            shift 2
            ;;
        monitor)
            COMMAND="monitor"
            shift
            ;;
        shell)
            COMMAND="shell"
            SERVICE=$2
            shift 2
            ;;
        *)
            print_color "❌ 未知命令: $1" $RED
            show_help
            exit 1
            ;;
    esac
done

# 检查Docker环境
check_docker

# 执行命令
case $COMMAND in
    build)
        check_env || exit 1
        build_images
        ;;
    start)
        check_env || exit 1
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    logs)
        view_logs $SERVICE
        ;;
    status)
        show_status
        ;;
    clean)
        clean_resources
        ;;
    backup)
        backup_data
        ;;
    restore)
        restore_data $BACKUP_DIR
        ;;
    monitor)
        start_monitoring
        ;;
    shell)
        enter_shell $SERVICE
        ;;
    *)
        print_color "❌ 请指定命令" $RED
        show_help
        exit 1
        ;;
esac
