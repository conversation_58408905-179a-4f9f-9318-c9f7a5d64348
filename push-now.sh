#!/bin/bash

echo "🚀 推送CoachTeacher到GitHub..."

# 检查远程仓库
if git remote get-url origin >/dev/null 2>&1; then
    echo "✅ 远程仓库已配置"
else
    echo "🔗 添加远程仓库..."
    git remote add origin https://github.com/aoof188/CoachTeacher.git
fi

# 确保在main分支
git branch -M main

# 推送代码
echo "📤 推送代码到GitHub..."
git push -u origin main

if [ $? -eq 0 ]; then
    echo "✅ 推送成功!"
    echo "🌐 项目地址: https://github.com/aoof188/CoachTeacher"
    
    # 创建标签
    echo "🏷️ 创建版本标签..."
    git tag -a v1.0.0 -m "CoachTeacher v1.0.0 - Initial Release"
    git push origin v1.0.0
    
    echo "🎉 完成! 您的项目现已在GitHub上!"
else
    echo "❌ 推送失败，请确保已在GitHub上创建仓库"
    echo "📝 请访问: https://github.com/new"
    echo "   仓库名称: CoachTeacher"
    echo "   设置为Public，不要添加README"
fi
