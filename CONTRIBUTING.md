# 🤝 贡献指南

感谢您对 CoachTeacher 项目的关注！我们欢迎所有形式的贡献。

## 🚀 如何贡献

### 报告问题
- 使用 [Issues](../../issues) 报告 bug
- 提供详细的重现步骤
- 包含错误日志和环境信息

### 功能建议
- 在 Issues 中提出新功能建议
- 详细描述功能需求和使用场景
- 讨论实现方案

### 代码贡献
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📋 开发规范

### 代码风格
- **Python**: 遵循 PEP 8 规范
- **JavaScript**: 使用 ES6+ 语法
- **提交信息**: 使用 [Conventional Commits](https://conventionalcommits.org/) 格式

### 提交信息格式
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型包括：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 分支命名
- `feature/功能名称` - 新功能开发
- `bugfix/问题描述` - bug 修复
- `hotfix/紧急修复` - 紧急修复
- `docs/文档更新` - 文档更新

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
python -m pytest tests/ -v

# 前端测试
cd miniprogram
# 在微信开发者工具中测试
```

### 添加测试
- 为新功能添加相应的测试用例
- 确保测试覆盖率不降低
- 测试应该清晰、可维护

## 📝 文档

### 更新文档
- 新功能需要更新相关文档
- API 变更需要更新 API 文档
- 重要变更需要更新 README

### 文档风格
- 使用清晰的标题结构
- 提供代码示例
- 包含必要的截图或图表

## 🔍 代码审查

### Pull Request 要求
- 提供清晰的 PR 描述
- 关联相关的 Issue
- 确保 CI 检查通过
- 请求适当的审查者

### 审查标准
- 代码质量和可读性
- 功能正确性
- 性能影响
- 安全考虑
- 文档完整性

## 🏗️ 开发环境

### 环境要求
- Python 3.8+
- Node.js 14+
- Docker & Docker Compose
- 微信开发者工具

### 快速开始
```bash
# 克隆项目
git clone https://github.com/YOUR_USERNAME/CoachTeacher.git
cd CoachTeacher

# 设置后端
cd backend
pip install -r requirements.txt
cp .env.example .env

# 启动服务
docker-compose up -d
```

## 📞 获取帮助

### 联系方式
- 创建 [Issue](../../issues) 提问
- 参与 [Discussions](../../discussions) 讨论
- 查看 [Wiki](../../wiki) 文档

### 社区准则
- 保持友善和尊重
- 建设性的反馈
- 帮助新贡献者
- 遵循项目价值观

## 🎯 贡献认可

### 贡献者列表
所有贡献者都会在项目中得到认可。

### 贡献类型
- 💻 代码贡献
- 📖 文档改进
- 🐛 问题报告
- 💡 功能建议
- 🎨 设计改进
- 🌍 国际化
- 📢 推广宣传

---

**感谢您的贡献！让我们一起让 CoachTeacher 变得更好！** 🚀
