version: '3.8'

services:
  # 后端API服务 - 生产配置
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: coachteacher-backend-prod
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - HOST=0.0.0.0
      - PORT=8000
      - WORKERS=4
    env_file:
      - ./backend/.env.prod
    volumes:
      - ./backend/logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    networks:
      - coachteacher-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - redis
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis缓存服务 - 生产配置
  redis:
    image: redis:7-alpine
    container_name: coachteacher-redis-prod
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - coachteacher-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx反向代理 - 生产配置
  nginx:
    image: nginx:alpine
    container_name: coachteacher-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      - /etc/localtime:/etc/localtime:ro
    networks:
      - coachteacher-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: coachteacher-prometheus-prod
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
      - /etc/localtime:/etc/localtime:ro
    networks:
      - coachteacher-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: coachteacher-grafana-prod
    ports:
      - "127.0.0.1:3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_SECURITY_SECRET_KEY=${GRAFANA_SECRET_KEY:-your-secret-key}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - coachteacher-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    depends_on:
      - prometheus
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 日志收集 - Filebeat (可选)
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: coachteacher-filebeat-prod
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/app:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - coachteacher-network
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    profiles:
      - logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  coachteacher-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: coachteacher-br
